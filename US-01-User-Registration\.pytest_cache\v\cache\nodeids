["tests/test_us01_registration.py::TestAuthRoutes::test_check_email_available", "tests/test_us01_registration.py::TestAuthRoutes::test_check_email_taken", "tests/test_us01_registration.py::TestAuthRoutes::test_health_check", "tests/test_us01_registration.py::TestAuthRoutes::test_register_duplicate_email", "tests/test_us01_registration.py::TestAuthRoutes::test_register_invalid_email", "tests/test_us01_registration.py::TestAuthRoutes::test_register_missing_email", "tests/test_us01_registration.py::TestAuthRoutes::test_register_missing_password", "tests/test_us01_registration.py::TestAuthRoutes::test_register_success", "tests/test_us01_registration.py::TestAuthRoutes::test_register_weak_password", "tests/test_us01_registration.py::TestUserModel::test_create_user_duplicate_email", "tests/test_us01_registration.py::TestUserModel::test_create_user_success", "tests/test_us01_registration.py::TestUserModel::test_create_user_weak_password", "tests/test_us01_registration.py::TestUserModel::test_email_normalization", "tests/test_us01_registration.py::TestUserModel::test_find_by_email", "tests/test_us01_registration.py::TestUserModel::test_password_hashing", "tests/test_us01_registration.py::TestUserModel::test_user_creation", "tests/test_us01_registration.py::TestUserModel::test_user_to_dict"]