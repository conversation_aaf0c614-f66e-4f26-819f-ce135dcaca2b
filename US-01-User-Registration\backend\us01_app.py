"""
US-01: User Registration - Flask Application
===========================================

This is the main Flask application file for US-01 User Registration.
It sets up the Flask app, database configuration, and registers routes.

Tech Stack: Flask, Flask-SQLAlchemy, PostgreSQL, Flask-CORS
"""

from flask import Flask, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
import os
from datetime import datetime

# Import our models and routes
from us01_user_model import db, User
from us01_auth_routes import auth_bp

def create_app(config_name='development'):
    """
    Application factory pattern for creating Flask app
    
    Args:
        config_name (str): Configuration environment ('development', 'testing', 'production')
        
    Returns:
        Flask: Configured Flask application
    """
    
    # Create Flask application
    app = Flask(__name__)
    
    # Configure CORS (Cross-Origin Resource Sharing)
    # This allows frontend to communicate with backend from different ports
    CORS(app, origins=['http://localhost:3000', 'http://127.0.0.1:3000'])
    
    # Database Configuration
    if config_name == 'testing':
        # Use SQLite for testing
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///test_dr_resume.db'
    else:
        # PostgreSQL configuration for development/production
        # Replace these with your actual database credentials
        DB_HOST = os.getenv('DB_HOST', 'localhost')
        DB_PORT = os.getenv('DB_PORT', '5432')
        DB_NAME = os.getenv('DB_NAME', 'dr_resume_db')
        DB_USER = os.getenv('DB_USER', 'postgres')
        DB_PASSWORD = os.getenv('DB_PASSWORD', 'your_password_here')
        
        app.config['SQLALCHEMY_DATABASE_URI'] = f'postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}'
    
    # SQLAlchemy Configuration
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SQLALCHEMY_ECHO'] = config_name == 'development'  # Log SQL queries in development
    
    # Security Configuration
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'your-secret-key-change-in-production')
    
    # Initialize extensions with app
    db.init_app(app)
    
    # Register Blueprints (route modules)
    app.register_blueprint(auth_bp)
    
    # Create database tables
    with app.app_context():
        try:
            db.create_all()
            print("✅ Database tables created successfully")
        except Exception as e:
            print(f"❌ Error creating database tables: {e}")
    
    # Root route
    @app.route('/')
    def home():
        """
        Home endpoint - provides API information
        """
        return jsonify({
            'success': True,
            'message': 'Dr. Resume - AI Resume Scanner API',
            'version': '1.0.0',
            'current_feature': 'US-01: User Registration',
            'endpoints': {
                'registration': '/api/register',
                'email_check': '/api/check-email',
                'health': '/api/health'
            },
            'timestamp': datetime.utcnow().isoformat()
        })
    
    # Global error handlers
    @app.errorhandler(404)
    def not_found(error):
        """Handle 404 errors globally"""
        return jsonify({
            'success': False,
            'message': 'API endpoint not found',
            'error': 'Not Found'
        }), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        """Handle 500 errors globally"""
        return jsonify({
            'success': False,
            'message': 'Internal server error',
            'error': 'Internal Server Error'
        }), 500
    
    @app.errorhandler(400)
    def bad_request(error):
        """Handle 400 errors globally"""
        return jsonify({
            'success': False,
            'message': 'Bad request',
            'error': 'Bad Request'
        }), 400
    
    # Health check endpoint
    @app.route('/health')
    def health():
        """
        Application health check
        """
        try:
            # Test database connection
            db.session.execute('SELECT 1')
            db_status = 'connected'
        except Exception as e:
            db_status = f'error: {str(e)}'
        
        return jsonify({
            'success': True,
            'message': 'Application is running',
            'status': {
                'app': 'running',
                'database': db_status,
                'feature': 'US-01: User Registration'
            },
            'timestamp': datetime.utcnow().isoformat()
        })
    
    return app

def run_development_server():
    """
    Run the Flask development server
    """
    app = create_app('development')
    
    print("🚀 Starting Dr. Resume API Server...")
    print("📋 Current Feature: US-01 User Registration")
    print("🌐 Server will be available at: http://localhost:5000")
    print("📚 API Documentation: http://localhost:5000")
    print("🔍 Health Check: http://localhost:5000/health")
    print("\n📋 Available Endpoints:")
    print("   POST /api/register - User registration")
    print("   POST /api/check-email - Check email availability")
    print("   GET /api/health - Service health check")
    print("\n⚠️  Make sure PostgreSQL is running and database is configured!")
    print("💡 Check the README_US01.md for setup instructions\n")
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        use_reloader=True
    )

if __name__ == '__main__':
    run_development_server()
