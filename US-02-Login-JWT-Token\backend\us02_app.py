"""
US-02: <PERSON><PERSON> + JWT Token - Flask Application
===========================================

This is the main Flask application file for US-02 Login + JWT Token.
It extends US-01 with JWT authentication capabilities.

Tech Stack: Flask, Flask-JWT-Extended, Flask-SQLAlchemy, PostgreSQL
"""

from flask import Flask, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
from flask_jwt_extended import JWTManager
import os
import sys
from datetime import datetime, timedelta

# Add US-01 backend to path to import User model
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-01-User-Registration', 'backend'))

# Import from US-01
from us01_user_model import db, User

# Import US-02 routes
from us02_auth_routes import (
    auth_bp, check_if_token_revoked, jwt_additional_claims_loader,
    expired_token_callback, invalid_token_callback, missing_token_callback
)

def create_app(config_name='development'):
    """
    Application factory pattern for creating Flask app with JWT support
    
    Args:
        config_name (str): Configuration environment ('development', 'testing', 'production')
        
    Returns:
        Flask: Configured Flask application with JWT
    """
    
    # Create Flask application
    app = Flask(__name__)
    
    # Configure CORS (Cross-Origin Resource Sharing)
    CORS(app, origins=['http://localhost:3000', 'http://127.0.0.1:3000'])
    
    # Database Configuration (same as US-01)
    if config_name == 'testing':
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///test_dr_resume_us02.db'
    else:
        # PostgreSQL configuration
        DB_HOST = os.getenv('DB_HOST', 'localhost')
        DB_PORT = os.getenv('DB_PORT', '5432')
        DB_NAME = os.getenv('DB_NAME', 'dr_resume_db')
        DB_USER = os.getenv('DB_USER', 'postgres')
        DB_PASSWORD = os.getenv('DB_PASSWORD', 'your_password_here')
        
        app.config['SQLALCHEMY_DATABASE_URI'] = f'postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}'
    
    # SQLAlchemy Configuration
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SQLALCHEMY_ECHO'] = config_name == 'development'
    
    # JWT Configuration
    app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', 'jwt-secret-string-change-in-production')
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=1)
    app.config['JWT_REFRESH_TOKEN_EXPIRES'] = timedelta(days=30)
    app.config['JWT_ALGORITHM'] = 'HS256'
    app.config['JWT_TOKEN_LOCATION'] = ['headers']
    app.config['JWT_HEADER_NAME'] = 'Authorization'
    app.config['JWT_HEADER_TYPE'] = 'Bearer'
    
    # Security Configuration
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'your-secret-key-change-in-production')
    
    # Initialize extensions with app
    db.init_app(app)
    
    # Initialize JWT Manager
    jwt = JWTManager(app)
    
    # Configure JWT callbacks
    jwt.token_in_blocklist_loader(check_if_token_revoked)
    jwt.additional_claims_loader(jwt_additional_claims_loader)
    jwt.expired_token_loader(expired_token_callback)
    jwt.invalid_token_loader(invalid_token_callback)
    jwt.unauthorized_loader(missing_token_callback)
    
    # Register Blueprints
    app.register_blueprint(auth_bp)
    
    # Create database tables
    with app.app_context():
        try:
            db.create_all()
            print("✅ Database tables created successfully (US-02)")
        except Exception as e:
            print(f"❌ Error creating database tables: {e}")
    
    # Root route
    @app.route('/')
    def home():
        """
        Home endpoint - provides API information for US-02
        """
        return jsonify({
            'success': True,
            'message': 'Dr. Resume - AI Resume Scanner API',
            'version': '1.0.0',
            'current_feature': 'US-02: Login + JWT Token',
            'previous_features': ['US-01: User Registration'],
            'endpoints': {
                'authentication': {
                    'login': 'POST /api/login',
                    'refresh': 'POST /api/refresh',
                    'logout': 'POST /api/logout',
                    'profile': 'GET /api/me',
                    'validate': 'POST /api/validate-token'
                },
                'registration': {
                    'register': 'POST /api/register',
                    'check_email': 'POST /api/check-email'
                }
            },
            'jwt_info': {
                'access_token_expires': '1 hour',
                'refresh_token_expires': '30 days',
                'token_location': 'Authorization header',
                'token_format': 'Bearer <token>'
            },
            'timestamp': datetime.utcnow().isoformat()
        })
    
    # Health check endpoint with JWT info
    @app.route('/health')
    def health():
        """
        Application health check with JWT status
        """
        try:
            # Test database connection
            db.session.execute('SELECT 1')
            db_status = 'connected'
        except Exception as e:
            db_status = f'error: {str(e)}'
        
        # Count users
        try:
            user_count = User.query.count()
        except Exception:
            user_count = 'unknown'
        
        return jsonify({
            'success': True,
            'message': 'Application is running',
            'status': {
                'app': 'running',
                'database': db_status,
                'jwt': 'configured',
                'feature': 'US-02: Login + JWT Token',
                'user_count': user_count
            },
            'features': {
                'us01_registration': 'active',
                'us02_jwt_auth': 'active'
            },
            'timestamp': datetime.utcnow().isoformat()
        })
    
    # Protected route example
    @app.route('/api/protected')
    @jwt_required()
    def protected():
        """
        Example protected route that requires JWT authentication
        """
        from flask_jwt_extended import get_jwt_identity, get_jwt
        
        current_user_id = get_jwt_identity()
        jwt_claims = get_jwt()
        
        return jsonify({
            'success': True,
            'message': 'This is a protected route',
            'user_id': current_user_id,
            'user_email': jwt_claims.get('email'),
            'user_role': jwt_claims.get('role'),
            'is_premium': jwt_claims.get('is_premium'),
            'timestamp': datetime.utcnow().isoformat()
        })
    
    # Global error handlers
    @app.errorhandler(404)
    def not_found(error):
        """Handle 404 errors globally"""
        return jsonify({
            'success': False,
            'message': 'API endpoint not found',
            'error': 'Not Found',
            'available_endpoints': [
                'POST /api/login',
                'POST /api/refresh',
                'POST /api/logout',
                'GET /api/me',
                'POST /api/validate-token',
                'POST /api/register',
                'POST /api/check-email'
            ]
        }), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        """Handle 500 errors globally"""
        return jsonify({
            'success': False,
            'message': 'Internal server error',
            'error': 'Internal Server Error'
        }), 500
    
    @app.errorhandler(400)
    def bad_request(error):
        """Handle 400 errors globally"""
        return jsonify({
            'success': False,
            'message': 'Bad request',
            'error': 'Bad Request'
        }), 400
    
    return app

def run_development_server():
    """
    Run the Flask development server for US-02
    """
    app = create_app('development')
    
    print("🚀 Starting Dr. Resume API Server (US-02)...")
    print("📋 Current Feature: US-02 Login + JWT Token")
    print("🔗 Previous Feature: US-01 User Registration")
    print("🌐 Server will be available at: http://localhost:5000")
    print("📚 API Documentation: http://localhost:5000")
    print("🔍 Health Check: http://localhost:5000/health")
    print("\n📋 Available Endpoints:")
    print("   🔐 Authentication:")
    print("     POST /api/login - User login with JWT")
    print("     POST /api/refresh - Refresh JWT token")
    print("     POST /api/logout - User logout")
    print("     GET /api/me - Get current user profile")
    print("     POST /api/validate-token - Validate JWT token")
    print("   👤 Registration (from US-01):")
    print("     POST /api/register - User registration")
    print("     POST /api/check-email - Check email availability")
    print("   🛡️  Protected Routes:")
    print("     GET /api/protected - Example protected endpoint")
    print("\n⚠️  Make sure PostgreSQL is running and US-01 is set up!")
    print("💡 Check the README_US02.md for setup instructions")
    print("🔑 JWT tokens expire in 1 hour (access) / 30 days (refresh)\n")
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        use_reloader=True
    )

if __name__ == '__main__':
    run_development_server()
