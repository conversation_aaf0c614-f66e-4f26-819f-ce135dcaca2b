# US-03: Resume Upload - Backend Dependencies
# ==========================================
# 
# This file contains all Python packages required for US-03 Resume Upload
# As per requirements: Flask, local storage (os, werkzeug), SQLAlchemy
# Includes all previous US dependencies plus file processing packages

# Core Flask Framework
Flask==2.3.3
Werkzeug==2.3.7

# Database ORM and PostgreSQL (as per requirements)
Flask-SQLAlchemy==3.0.5
SQLAlchemy==2.0.21
psycopg2-binary==2.9.7

# CORS support for frontend communication
Flask-CORS==4.0.0

# JWT Authentication (from US-02)
Flask-JWT-Extended==4.5.3
PyJWT==2.8.0

# File Processing (as per requirements: "Parse file using local script")
PyPDF2==3.0.1
python-docx==0.8.11

# Environment variables management
python-dotenv==1.0.0

# Development and Testing Dependencies
pytest==7.4.2
pytest-flask==1.2.0
pytest-cov==4.1.0

# Code formatting and linting (optional but recommended)
black==23.7.0
flake8==6.0.0

# For future US features (NLP, ML, etc.)
# These will be used in upcoming user stories
nltk==3.8.1
spacy==3.6.1
scikit-learn==1.3.0
requests==2.31.0

# Additional file processing utilities
filetype==1.2.0  # File type detection
Pillow==10.0.1   # Image processing (for future features)

# Security and utilities
cryptography==41.0.4  # For enhanced security
itsdangerous==2.1.2   # For secure token generation
