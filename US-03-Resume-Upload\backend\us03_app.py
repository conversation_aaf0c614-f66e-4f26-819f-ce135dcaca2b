"""
US-03: Resume Upload - Main Flask Application
============================================

This file creates the Flask application for US-03 Resume Upload functionality.
As per requirements: Flask, local storage (os, werkzeug), SQLAlchemy

Tech Stack: Flask, Flask-SQLAlchemy, Flask-JWT-Extended, Flask-CORS
"""

import os
import sys
from datetime import datetime, timedelta
from flask import Flask, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import JWTManager
from flask_cors import CORS
from dotenv import load_dotenv

# Add paths for imports from previous US
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-01-User-Registration', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-02-Login-JWT-Token', 'backend'))

try:
    from us01_user_model import db, User
    from us02_auth_routes import auth_bp
    from us02_auth_routes import (
        check_if_token_revoked, jwt_additional_claims_loader,
        expired_token_callback, invalid_token_callback, missing_token_callback
    )
except ImportError as e:
    print(f"⚠️  Import warning: {e}")
    print("Some features may not work without US-01 and US-02")
    db = SQLAlchemy()
    auth_bp = None

from us03_resume_model import Resume
from us03_upload_routes import upload_bp
from us03_file_utils import init_file_processor

def create_app(config_name='development'):
    """
    Create Flask application for US-03 Resume Upload
    
    Args:
        config_name (str): Configuration environment
        
    Returns:
        Flask: Configured Flask application
    """
    
    # Load environment variables
    load_dotenv()
    
    # Create Flask app
    app = Flask(__name__)
    
    # Configure CORS (allow frontend communication)
    CORS(app, origins=[
        'http://localhost:3002',
        'http://127.0.0.1:3002',
        'http://localhost:3000',
        'http://127.0.0.1:3000'
    ])
    
    # Database Configuration
    if config_name == 'testing':
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///test_dr_resume_us03.db'
    else:
        # PostgreSQL configuration (as per requirements)
        db_host = os.getenv('DB_HOST', 'localhost')
        db_port = os.getenv('DB_PORT', '5432')
        db_name = os.getenv('DB_NAME', 'dr_resume_db')
        db_user = os.getenv('DB_USER', 'postgres')
        db_password = os.getenv('DB_PASSWORD', 'password')
        
        app.config['SQLALCHEMY_DATABASE_URI'] = f'postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}'
    
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    # Security Configuration
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'us03-secret-key-change-in-production')
    app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', 'us03-jwt-secret-key')
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=1)
    app.config['JWT_REFRESH_TOKEN_EXPIRES'] = timedelta(days=30)
    
    # File Upload Configuration (as per requirements: local file system)
    upload_folder = os.getenv('UPLOAD_FOLDER', os.path.join(os.path.dirname(__file__), '..', 'uploads', 'resumes'))
    app.config['UPLOAD_FOLDER'] = os.path.abspath(upload_folder)
    app.config['MAX_CONTENT_LENGTH'] = int(os.getenv('MAX_CONTENT_LENGTH', 10 * 1024 * 1024))  # 10MB
    
    # Initialize extensions
    db.init_app(app)
    
    # Initialize JWT Manager
    jwt = JWTManager(app)
    
    # Configure JWT callbacks (if available from US-02)
    if 'check_if_token_revoked' in globals():
        jwt.token_in_blocklist_loader(check_if_token_revoked)
        jwt.additional_claims_loader(jwt_additional_claims_loader)
        jwt.expired_token_loader(expired_token_callback)
        jwt.invalid_token_loader(invalid_token_callback)
        jwt.unauthorized_loader(missing_token_callback)
    
    # Initialize file processor (as per requirements: local storage)
    init_file_processor(app.config['UPLOAD_FOLDER'])
    
    # Register Blueprints
    if auth_bp:
        app.register_blueprint(auth_bp)  # From US-02 (includes US-01 registration)
    app.register_blueprint(upload_bp)  # US-03 upload routes
    
    # Create database tables and upload directory
    with app.app_context():
        try:
            db.create_all()
            print("✅ Database tables created successfully (US-03)")
            
            # Ensure upload directory exists (as per requirements: /uploads/resumes/)
            os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
            print(f"✅ Upload directory ready: {app.config['UPLOAD_FOLDER']}")
            
        except Exception as e:
            print(f"❌ Error during initialization: {e}")
    
    # Root endpoint
    @app.route('/')
    def home():
        """Home endpoint with API information"""
        return jsonify({
            'success': True,
            'message': 'Dr. Resume API - US-03: Resume Upload',
            'version': '1.0.0',
            'features': {
                'us01_registration': 'active' if auth_bp else 'not_available',
                'us02_jwt_auth': 'active' if auth_bp else 'not_available',
                'us03_file_upload': 'active'
            },
            'endpoints': {
                'upload': 'POST /api/upload_resume',
                'list_resumes': 'GET /api/resumes',
                'resume_details': 'GET /api/resumes/<id>',
                'download': 'GET /api/resumes/<id>/download',
                'update': 'PUT /api/resumes/<id>',
                'delete': 'DELETE /api/resumes/<id>',
                'reprocess': 'POST /api/resumes/<id>/reprocess'
            },
            'upload_config': {
                'max_file_size': '10MB',
                'upload_folder': app.config['UPLOAD_FOLDER'],
                'supported_formats': ['PDF', 'DOC', 'DOCX']
            }
        })
    
    # Health check endpoint
    @app.route('/health')
    def health_check():
        """Health check endpoint"""
        
        # Check database connection
        try:
            db.session.execute('SELECT 1')
            db_status = 'connected'
        except Exception:
            db_status = 'disconnected'
        
        # Check upload directory
        upload_dir_status = 'ready' if os.path.exists(app.config['UPLOAD_FOLDER']) else 'not_found'
        
        # Count users and resumes
        try:
            if User:
                user_count = User.query.count()
            else:
                user_count = 'not_available'
            resume_count = Resume.query.count()
        except Exception:
            user_count = 'unknown'
            resume_count = 'unknown'
        
        return jsonify({
            'success': True,
            'message': 'Application is running',
            'status': {
                'app': 'running',
                'database': db_status,
                'jwt': 'configured',
                'upload_directory': upload_dir_status,
                'feature': 'US-03: Resume Upload with Local Storage',
                'user_count': user_count,
                'resume_count': resume_count
            },
            'features': {
                'us01_registration': 'active' if auth_bp else 'not_available',
                'us02_jwt_auth': 'active' if auth_bp else 'not_available',
                'us03_file_upload': 'active'
            },
            'upload_config': {
                'max_file_size': '10MB',
                'upload_folder': app.config['UPLOAD_FOLDER'],
                'supported_formats': ['PDF', 'DOC', 'DOCX']
            },
            'timestamp': datetime.utcnow().isoformat()
        })
    
    # Global error handlers
    @app.errorhandler(404)
    def not_found(error):
        """Handle 404 errors globally"""
        return jsonify({
            'success': False,
            'message': 'API endpoint not found',
            'error': 'Not Found',
            'available_endpoints': [
                'POST /api/upload_resume',
                'GET /api/resumes',
                'GET /api/resumes/<id>',
                'POST /api/login',
                'POST /api/register'
            ]
        }), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        """Handle 500 errors globally"""
        return jsonify({
            'success': False,
            'message': 'Internal server error',
            'error': 'Internal Server Error'
        }), 500
    
    @app.errorhandler(413)
    def file_too_large(error):
        """Handle file too large errors"""
        return jsonify({
            'success': False,
            'message': 'File too large. Maximum size is 10MB.',
            'error': 'Payload Too Large'
        }), 413
    
    return app

def run_development_server():
    """
    Run the Flask development server for US-03
    """
    app = create_app('development')
    
    print("🚀 Starting Dr. Resume API Server (US-03)...")
    print("📋 Current Feature: US-03 Resume Upload with Local Storage")
    print("🔗 Previous Features: US-01 User Registration, US-02 Login + JWT Token")
    print("🌐 Server will be available at: http://localhost:5002")
    print("📚 API Documentation: http://localhost:5002")
    print("🔍 Health Check: http://localhost:5002/health")
    print("\n📋 Available Endpoints:")
    print("   📁 File Upload:")
    print("     POST /api/upload_resume - Upload resume file (as per requirements)")
    print("     GET /api/resumes - Get user's resumes")
    print("     GET /api/resumes/<id> - Get resume details")
    print("     GET /api/resumes/<id>/download - Download resume")
    print("     PUT /api/resumes/<id> - Update resume metadata")
    print("     DELETE /api/resumes/<id> - Delete resume")
    print("     POST /api/resumes/<id>/reprocess - Reprocess text extraction")
    print("   🔐 Authentication (from US-02):")
    print("     POST /api/login - User login with JWT")
    print("     POST /api/refresh - Refresh JWT token")
    print("     POST /api/logout - User logout")
    print("     GET /api/me - Get current user profile")
    print("   👤 Registration (from US-01):")
    print("     POST /api/register - User registration")
    print("     POST /api/check-email - Check email availability")
    print("\n📁 File Upload Info:")
    print("   📄 Supported Formats: PDF, DOC, DOCX (as per requirements)")
    print("   📏 Maximum Size: 10MB")
    print("   🔍 Text Extraction: Automatic (local script)")
    print("   💾 Storage: Local file system (/uploads/resumes/)")
    print("\n⚠️  Make sure you have PyPDF2 and python-docx installed!")
    print("💡 Check the README_US03.md for setup instructions\n")
    
    app.run(
        host='0.0.0.0',
        port=5002,
        debug=True,
        use_reloader=True
    )

if __name__ == '__main__':
    run_development_server()
