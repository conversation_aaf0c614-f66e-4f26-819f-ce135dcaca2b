"""
US-03: Resume Upload - File Processing Utilities
===============================================

This file contains utilities for file upload, validation, and text extraction.
As per requirements: "Parse file using local script" and "Save resume file to local file system"

Tech Stack: os, werkzeug, PyPDF2, python-docx
"""

import os
import uuid
import mimetypes
from datetime import datetime
from werkzeug.utils import secure_filename
from werkzeug.datastructures import FileStorage

# File processing libraries (as per requirements)
try:
    import PyPDF2
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

try:
    import docx
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

class FileProcessor:
    """
    File Processing Class for US-03 Resume Upload
    
    Handles file upload, validation, storage, and text extraction
    as per requirements: local file system storage and parsing
    """
    
    # Allowed file extensions (as per requirements: PDF/DOC)
    ALLOWED_EXTENSIONS = {'.pdf', '.doc', '.docx'}
    
    # MIME types for validation
    ALLOWED_MIME_TYPES = {
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    }
    
    # Maximum file size (10MB)
    MAX_FILE_SIZE = 10 * 1024 * 1024
    
    def __init__(self, upload_folder):
        """
        Initialize FileProcessor
        
        Args:
            upload_folder (str): Path to upload directory
        """
        self.upload_folder = upload_folder
        self.ensure_upload_directory()
    
    def ensure_upload_directory(self):
        """
        Ensure upload directory exists (as per requirements: /uploads/resumes/)
        """
        try:
            os.makedirs(self.upload_folder, exist_ok=True)
            print(f"✅ Upload directory ready: {self.upload_folder}")
        except Exception as e:
            print(f"❌ Error creating upload directory: {e}")
            raise
    
    def is_allowed_file(self, filename):
        """
        Check if file type is allowed (as per requirements: PDF/DOC)
        
        Args:
            filename (str): Name of the file
            
        Returns:
            bool: True if file type is allowed
        """
        if not filename:
            return False
        
        # Check extension
        _, ext = os.path.splitext(filename.lower())
        return ext in self.ALLOWED_EXTENSIONS
    
    def validate_file(self, file_obj):
        """
        Validate uploaded file
        
        Args:
            file_obj (FileStorage): Uploaded file object
            
        Returns:
            tuple: (is_valid, error_message, file_info)
        """
        try:
            # Check if file exists
            if not file_obj or not file_obj.filename:
                return False, "No file provided", None
            
            # Check filename
            if file_obj.filename == '':
                return False, "No file selected", None
            
            # Check file type
            if not self.is_allowed_file(file_obj.filename):
                return False, f"File type not allowed. Supported: {', '.join(self.ALLOWED_EXTENSIONS)}", None
            
            # Check file size
            file_obj.seek(0, os.SEEK_END)
            file_size = file_obj.tell()
            file_obj.seek(0)
            
            if file_size > self.MAX_FILE_SIZE:
                max_mb = self.MAX_FILE_SIZE / (1024 * 1024)
                return False, f"File too large. Maximum size: {max_mb}MB", None
            
            if file_size == 0:
                return False, "File is empty", None
            
            # Get file info
            file_info = {
                'original_filename': file_obj.filename,
                'file_size': file_size,
                'file_type': file_obj.content_type or mimetypes.guess_type(file_obj.filename)[0],
                'file_extension': os.path.splitext(file_obj.filename.lower())[1]
            }
            
            return True, None, file_info
            
        except Exception as e:
            return False, f"File validation error: {str(e)}", None
    
    def generate_secure_filename(self, original_filename, user_id):
        """
        Generate secure filename for storage
        
        Args:
            original_filename (str): Original filename
            user_id (str): User UUID
            
        Returns:
            str: Secure filename
        """
        # Get file extension
        _, ext = os.path.splitext(original_filename)
        
        # Generate secure filename: user_id_timestamp_uuid.ext
        timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
        unique_id = str(uuid.uuid4())[:8]
        
        secure_name = f"{user_id}_{timestamp}_{unique_id}{ext}"
        return secure_name
    
    def save_file(self, file_obj, user_id):
        """
        Save uploaded file to local file system (as per requirements)
        
        Args:
            file_obj (FileStorage): Uploaded file object
            user_id (str): User UUID
            
        Returns:
            tuple: (success, error_message, file_data)
        """
        try:
            # Validate file
            is_valid, error, file_info = self.validate_file(file_obj)
            if not is_valid:
                return False, error, None
            
            # Generate secure filename
            stored_filename = self.generate_secure_filename(file_info['original_filename'], user_id)
            file_path = os.path.join(self.upload_folder, stored_filename)
            
            # Save file to local file system
            file_obj.save(file_path)
            
            # Prepare file data
            file_data = {
                'original_filename': file_info['original_filename'],
                'stored_filename': stored_filename,
                'file_path': file_path,
                'file_size': file_info['file_size'],
                'file_type': file_info['file_type'],
                'file_extension': file_info['file_extension']
            }
            
            print(f"✅ File saved: {stored_filename}")
            return True, None, file_data
            
        except Exception as e:
            return False, f"File save error: {str(e)}", None
    
    def extract_text(self, file_path, file_extension):
        """
        Extract text from uploaded file (as per requirements: "Parse file using local script")
        
        Args:
            file_path (str): Path to the file
            file_extension (str): File extension
            
        Returns:
            tuple: (success, extracted_text, error_message)
        """
        try:
            if file_extension.lower() == '.pdf':
                return self._extract_pdf_text(file_path)
            elif file_extension.lower() in ['.doc', '.docx']:
                return self._extract_docx_text(file_path)
            else:
                return False, None, f"Unsupported file type: {file_extension}"
                
        except Exception as e:
            return False, None, f"Text extraction error: {str(e)}"
    
    def _extract_pdf_text(self, file_path):
        """
        Extract text from PDF file using PyPDF2
        
        Args:
            file_path (str): Path to PDF file
            
        Returns:
            tuple: (success, extracted_text, error_message)
        """
        if not PDF_AVAILABLE:
            return False, None, "PyPDF2 not installed. Install with: pip install PyPDF2"
        
        try:
            extracted_text = ""
            
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                # Extract text from all pages
                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num]
                    page_text = page.extract_text()
                    extracted_text += page_text + "\n"
            
            # Clean up text
            extracted_text = extracted_text.strip()
            
            if not extracted_text:
                return False, None, "No text found in PDF file"
            
            print(f"✅ PDF text extracted: {len(extracted_text)} characters")
            return True, extracted_text, None
            
        except Exception as e:
            return False, None, f"PDF extraction error: {str(e)}"
    
    def _extract_docx_text(self, file_path):
        """
        Extract text from DOCX file using python-docx
        
        Args:
            file_path (str): Path to DOCX file
            
        Returns:
            tuple: (success, extracted_text, error_message)
        """
        if not DOCX_AVAILABLE:
            return False, None, "python-docx not installed. Install with: pip install python-docx"
        
        try:
            # Handle both .doc and .docx (python-docx only supports .docx)
            if file_path.lower().endswith('.doc'):
                return False, None, "Legacy .doc files not supported. Please convert to .docx format"
            
            doc = docx.Document(file_path)
            extracted_text = ""
            
            # Extract text from paragraphs
            for paragraph in doc.paragraphs:
                extracted_text += paragraph.text + "\n"
            
            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        extracted_text += cell.text + " "
                    extracted_text += "\n"
            
            # Clean up text
            extracted_text = extracted_text.strip()
            
            if not extracted_text:
                return False, None, "No text found in DOCX file"
            
            print(f"✅ DOCX text extracted: {len(extracted_text)} characters")
            return True, extracted_text, None
            
        except Exception as e:
            return False, None, f"DOCX extraction error: {str(e)}"
    
    def delete_file(self, file_path):
        """
        Delete file from local file system
        
        Args:
            file_path (str): Path to file to delete
            
        Returns:
            bool: True if successful
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"✅ File deleted: {file_path}")
                return True
            return False
        except Exception as e:
            print(f"❌ Error deleting file: {e}")
            return False
    
    def get_file_info(self, file_path):
        """
        Get information about a stored file
        
        Args:
            file_path (str): Path to file
            
        Returns:
            dict: File information or None
        """
        try:
            if not os.path.exists(file_path):
                return None
            
            stat = os.stat(file_path)
            return {
                'exists': True,
                'size': stat.st_size,
                'modified': datetime.fromtimestamp(stat.st_mtime),
                'path': file_path
            }
        except Exception as e:
            return None

# Global file processor instance
file_processor = None

def init_file_processor(upload_folder):
    """
    Initialize global file processor instance
    
    Args:
        upload_folder (str): Upload directory path
    """
    global file_processor
    file_processor = FileProcessor(upload_folder)
    return file_processor

def get_file_processor():
    """
    Get global file processor instance
    
    Returns:
        FileProcessor: File processor instance
    """
    return file_processor
