"""
US-03: Resume Upload - Resume Model
==================================

This file defines the Resume model for storing uploaded resume files and metadata.
As per requirements: Store file path and metadata in DB using SQLAlchemy.

Tech Stack: Flask-SQLAlchemy, PostgreSQL
"""

import os
import uuid
from datetime import datetime
from sqlalchemy import Column, String, Integer, Text, DateTime, Boolean, ForeignKey
from sqlalchemy.orm import relationship
import sys

# Add US-01 path for User model import
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-01-User-Registration', 'backend'))

try:
    from us01_user_model import db, User
except ImportError:
    # Fallback for testing
    from flask_sqlalchemy import SQLAlchemy
    db = SQLAlchemy()

class Resume(db.Model):
    """
    Resume Model for US-03
    
    Stores uploaded resume files metadata and file paths as per requirements:
    - File path storage (local file system)
    - Metadata storage (filename, size, type)
    - User association (foreign key to users table)
    - Upload tracking (timestamps, status)
    """
    
    __tablename__ = 'resumes'
    
    # Primary key - UUID for security
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Foreign key to users table (from US-01)
    user_id = Column(String(36), ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    
    # File information (as per requirements)
    original_filename = Column(String(255), nullable=False)  # User's original filename
    stored_filename = Column(String(255), nullable=False)    # Secure stored filename
    file_path = Column(String(500), nullable=False)          # Full path to file (requirement)
    file_size = Column(Integer, nullable=False)              # File size in bytes
    file_type = Column(String(50), nullable=False)           # MIME type
    file_extension = Column(String(10), nullable=False)      # .pdf, .doc, .docx
    
    # Parsed content (as per requirements: "Parse file using local script")
    extracted_text = Column(Text)                            # Extracted text content
    extraction_status = Column(String(20), default='pending') # pending, success, failed
    extraction_error = Column(Text)                          # Error message if failed
    
    # Resume metadata
    resume_title = Column(String(200))                       # User-provided title
    resume_description = Column(Text)                        # User-provided description
    
    # Upload tracking
    upload_status = Column(String(20), default='uploaded')   # uploaded, processing, processed
    is_active = Column(Boolean, default=True)                # Soft delete flag
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationship to User model
    user = relationship("User", back_populates="resumes")
    
    def __init__(self, user_id, original_filename, stored_filename, file_path, 
                 file_size, file_type, file_extension, resume_title=None, 
                 resume_description=None):
        """
        Initialize Resume instance
        
        Args:
            user_id (str): UUID of the user who uploaded the resume
            original_filename (str): Original filename from user
            stored_filename (str): Secure filename for storage
            file_path (str): Full path where file is stored
            file_size (int): File size in bytes
            file_type (str): MIME type of the file
            file_extension (str): File extension (.pdf, .doc, .docx)
            resume_title (str, optional): User-provided title
            resume_description (str, optional): User-provided description
        """
        self.user_id = user_id
        self.original_filename = original_filename
        self.stored_filename = stored_filename
        self.file_path = file_path
        self.file_size = file_size
        self.file_type = file_type
        self.file_extension = file_extension
        self.resume_title = resume_title or original_filename
        self.resume_description = resume_description
    
    def __repr__(self):
        return f'<Resume {self.id}: {self.original_filename}>'
    
    @classmethod
    def create_resume(cls, user_id, original_filename, stored_filename, file_path,
                     file_size, file_type, file_extension, resume_title=None,
                     resume_description=None):
        """
        Create a new resume record
        
        Args:
            user_id (str): UUID of the user
            original_filename (str): Original filename
            stored_filename (str): Secure stored filename
            file_path (str): Full file path
            file_size (int): File size in bytes
            file_type (str): MIME type
            file_extension (str): File extension
            resume_title (str, optional): Resume title
            resume_description (str, optional): Resume description
            
        Returns:
            tuple: (Resume instance, error_message)
        """
        try:
            # Validate user exists
            user = User.query.get(user_id)
            if not user:
                return None, "User not found"
            
            # Create resume instance
            resume = cls(
                user_id=user_id,
                original_filename=original_filename,
                stored_filename=stored_filename,
                file_path=file_path,
                file_size=file_size,
                file_type=file_type,
                file_extension=file_extension,
                resume_title=resume_title,
                resume_description=resume_description
            )
            
            # Save to database
            db.session.add(resume)
            db.session.commit()
            
            return resume, None
            
        except Exception as e:
            db.session.rollback()
            return None, f"Database error: {str(e)}"
    
    @classmethod
    def find_by_user(cls, user_id):
        """
        Find all resumes for a specific user
        
        Args:
            user_id (str): User UUID
            
        Returns:
            list: List of Resume instances
        """
        return cls.query.filter_by(user_id=user_id, is_active=True).order_by(cls.created_at.desc()).all()
    
    @classmethod
    def find_by_id(cls, resume_id, user_id=None):
        """
        Find resume by ID, optionally filtered by user
        
        Args:
            resume_id (str): Resume UUID
            user_id (str, optional): User UUID for access control
            
        Returns:
            Resume: Resume instance or None
        """
        query = cls.query.filter_by(id=resume_id, is_active=True)
        if user_id:
            query = query.filter_by(user_id=user_id)
        return query.first()
    
    def update_extraction_status(self, status, extracted_text=None, error_message=None):
        """
        Update text extraction status
        
        Args:
            status (str): 'pending', 'success', or 'failed'
            extracted_text (str, optional): Extracted text content
            error_message (str, optional): Error message if failed
        """
        try:
            self.extraction_status = status
            if extracted_text:
                self.extracted_text = extracted_text
            if error_message:
                self.extraction_error = error_message
            self.updated_at = datetime.utcnow()
            
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            return False
    
    def soft_delete(self):
        """
        Soft delete the resume (mark as inactive)
        """
        try:
            self.is_active = False
            self.updated_at = datetime.utcnow()
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            return False
    
    def to_dict(self):
        """
        Convert Resume instance to dictionary for JSON serialization
        
        Returns:
            dict: Resume data as dictionary
        """
        return {
            'id': self.id,
            'user_id': self.user_id,
            'original_filename': self.original_filename,
            'stored_filename': self.stored_filename,
            'file_path': self.file_path,
            'file_size': self.file_size,
            'file_type': self.file_type,
            'file_extension': self.file_extension,
            'extracted_text': self.extracted_text,
            'extraction_status': self.extraction_status,
            'extraction_error': self.extraction_error,
            'resume_title': self.resume_title,
            'resume_description': self.resume_description,
            'upload_status': self.upload_status,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

# Add relationship to User model (if not already present)
try:
    if not hasattr(User, 'resumes'):
        User.resumes = relationship("Resume", back_populates="user", cascade="all, delete-orphan")
except:
    pass
