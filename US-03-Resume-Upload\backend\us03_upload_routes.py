"""
US-03: Resume Upload - Upload Routes
===================================

This file defines the upload routes for US-03 Resume Upload functionality.
As per requirements: POST /api/upload_resume endpoint

Tech Stack: Flask, local storage (os, werkzeug), SQLAlchemy
"""

import os
import sys
from flask import Blueprint, request, jsonify, send_file
from flask_jwt_extended import jwt_required, get_jwt_identity

# Add paths for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-01-User-Registration', 'backend'))

try:
    from us01_user_model import User, db
except ImportError:
    # Fallback for testing
    from flask_sqlalchemy import SQLAlchemy
    db = SQLAlchemy()
    User = None

from us03_resume_model import Resume
from us03_file_utils import get_file_processor

# Create Blueprint for upload routes
upload_bp = Blueprint('upload', __name__, url_prefix='/api')

@upload_bp.route('/upload_resume', methods=['POST'])
@jwt_required()
def upload_resume():
    """
    Resume Upload Endpoint (as per requirements: POST /api/upload_resume)
    
    Handles file upload, validation, storage, and text extraction.
    Requirements:
    - Save resume file to local file system (e.g., /uploads/resumes/)
    - Parse file using local script
    - Store file path and metadata in DB
    
    Expected form data:
    - file: Resume file (PDF/DOC/DOCX)
    - title: Optional resume title
    - description: Optional resume description
    
    Returns:
        JSON response with upload status and resume data
    """
    try:
        # Get current user from JWT token
        current_user_id = get_jwt_identity()
        
        # Verify user exists
        if User:
            user = User.query.get(current_user_id)
            if not user:
                return jsonify({
                    'success': False,
                    'message': 'User not found'
                }), 401
        
        # Check if file is present in request
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'message': 'No file provided'
            }), 400
        
        file_obj = request.files['file']
        
        # Get optional metadata
        title = request.form.get('title', '').strip() or None
        description = request.form.get('description', '').strip() or None
        
        # Get file processor
        processor = get_file_processor()
        if not processor:
            return jsonify({
                'success': False,
                'message': 'File processor not initialized'
            }), 500
        
        # Save file to local file system (as per requirements)
        success, error, file_data = processor.save_file(file_obj, current_user_id)
        
        if not success:
            return jsonify({
                'success': False,
                'message': error
            }), 400
        
        # Store file path and metadata in DB (as per requirements)
        resume, db_error = Resume.create_resume(
            user_id=current_user_id,
            original_filename=file_data['original_filename'],
            stored_filename=file_data['stored_filename'],
            file_path=file_data['file_path'],
            file_size=file_data['file_size'],
            file_type=file_data['file_type'],
            file_extension=file_data['file_extension'],
            resume_title=title,
            resume_description=description
        )
        
        if db_error:
            # Clean up uploaded file if database save failed
            processor.delete_file(file_data['file_path'])
            return jsonify({
                'success': False,
                'message': f'Database error: {db_error}'
            }), 500
        
        # Parse file using local script (as per requirements)
        extract_success, extracted_text, extract_error = processor.extract_text(
            resume.file_path, resume.file_extension
        )
        
        # Update extraction status
        if extract_success:
            resume.update_extraction_status('success', extracted_text=extracted_text)
        else:
            resume.update_extraction_status('failed', error_message=extract_error)
        
        # Return success response
        return jsonify({
            'success': True,
            'message': 'Resume uploaded successfully',
            'resume': resume.to_dict()
        }), 201
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Upload failed: {str(e)}'
        }), 500

@upload_bp.route('/resumes', methods=['GET'])
@jwt_required()
def get_user_resumes():
    """
    Get all resumes for the current user
    
    Returns:
        JSON response with list of user's resumes
    """
    try:
        current_user_id = get_jwt_identity()
        
        # Get user's resumes
        resumes = Resume.find_by_user(current_user_id)
        
        return jsonify({
            'success': True,
            'message': f'Found {len(resumes)} resumes',
            'resumes': [resume.to_dict() for resume in resumes]
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to get resumes: {str(e)}'
        }), 500

@upload_bp.route('/resumes/<resume_id>', methods=['GET'])
@jwt_required()
def get_resume_details(resume_id):
    """
    Get details of a specific resume
    
    Args:
        resume_id (str): Resume UUID
        
    Returns:
        JSON response with resume details
    """
    try:
        current_user_id = get_jwt_identity()
        
        # Find resume (with user access control)
        resume = Resume.find_by_id(resume_id, user_id=current_user_id)
        
        if not resume:
            return jsonify({
                'success': False,
                'message': 'Resume not found'
            }), 404
        
        return jsonify({
            'success': True,
            'message': 'Resume found',
            'resume': resume.to_dict()
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to get resume: {str(e)}'
        }), 500

@upload_bp.route('/resumes/<resume_id>/download', methods=['GET'])
@jwt_required()
def download_resume(resume_id):
    """
    Download original resume file
    
    Args:
        resume_id (str): Resume UUID
        
    Returns:
        File download or JSON error
    """
    try:
        current_user_id = get_jwt_identity()
        
        # Find resume (with user access control)
        resume = Resume.find_by_id(resume_id, user_id=current_user_id)
        
        if not resume:
            return jsonify({
                'success': False,
                'message': 'Resume not found'
            }), 404
        
        # Check if file exists
        if not os.path.exists(resume.file_path):
            return jsonify({
                'success': False,
                'message': 'Resume file not found on disk'
            }), 404
        
        # Send file
        return send_file(
            resume.file_path,
            as_attachment=True,
            download_name=resume.original_filename,
            mimetype=resume.file_type
        )
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Download failed: {str(e)}'
        }), 500

@upload_bp.route('/resumes/<resume_id>', methods=['PUT'])
@jwt_required()
def update_resume(resume_id):
    """
    Update resume metadata
    
    Args:
        resume_id (str): Resume UUID
        
    Returns:
        JSON response with updated resume data
    """
    try:
        current_user_id = get_jwt_identity()
        
        # Find resume (with user access control)
        resume = Resume.find_by_id(resume_id, user_id=current_user_id)
        
        if not resume:
            return jsonify({
                'success': False,
                'message': 'Resume not found'
            }), 404
        
        # Get update data
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400
        
        # Update fields
        if 'title' in data:
            resume.resume_title = data['title'].strip() or resume.original_filename
        
        if 'description' in data:
            resume.resume_description = data['description'].strip() or None
        
        # Save changes
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Resume updated successfully',
            'resume': resume.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'Update failed: {str(e)}'
        }), 500

@upload_bp.route('/resumes/<resume_id>', methods=['DELETE'])
@jwt_required()
def delete_resume(resume_id):
    """
    Delete resume (soft delete)
    
    Args:
        resume_id (str): Resume UUID
        
    Returns:
        JSON response with deletion status
    """
    try:
        current_user_id = get_jwt_identity()
        
        # Find resume (with user access control)
        resume = Resume.find_by_id(resume_id, user_id=current_user_id)
        
        if not resume:
            return jsonify({
                'success': False,
                'message': 'Resume not found'
            }), 404
        
        # Soft delete
        if resume.soft_delete():
            return jsonify({
                'success': True,
                'message': 'Resume deleted successfully'
            }), 200
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to delete resume'
            }), 500
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Delete failed: {str(e)}'
        }), 500

@upload_bp.route('/resumes/<resume_id>/reprocess', methods=['POST'])
@jwt_required()
def reprocess_resume(resume_id):
    """
    Reprocess text extraction for a resume
    
    Args:
        resume_id (str): Resume UUID
        
    Returns:
        JSON response with reprocessing status
    """
    try:
        current_user_id = get_jwt_identity()
        
        # Find resume (with user access control)
        resume = Resume.find_by_id(resume_id, user_id=current_user_id)
        
        if not resume:
            return jsonify({
                'success': False,
                'message': 'Resume not found'
            }), 404
        
        # Check if file exists
        if not os.path.exists(resume.file_path):
            return jsonify({
                'success': False,
                'message': 'Resume file not found on disk'
            }), 404
        
        # Get file processor
        processor = get_file_processor()
        if not processor:
            return jsonify({
                'success': False,
                'message': 'File processor not initialized'
            }), 500
        
        # Reprocess text extraction
        extract_success, extracted_text, extract_error = processor.extract_text(
            resume.file_path, resume.file_extension
        )
        
        # Update extraction status
        if extract_success:
            resume.update_extraction_status('success', extracted_text=extracted_text)
        else:
            resume.update_extraction_status('failed', error_message=extract_error)
        
        return jsonify({
            'success': True,
            'message': 'Text extraction reprocessed successfully',
            'resume': resume.to_dict()
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Reprocessing failed: {str(e)}'
        }), 500
