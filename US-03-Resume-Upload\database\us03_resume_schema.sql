-- US-03: Resume Upload - Database Schema
-- =======================================
-- 
-- This file contains the PostgreSQL database schema for US-03 Resume Upload
-- As per requirements: Store file path and metadata in DB using SQLAlchemy
-- 
-- Tech Stack: PostgreSQL 13+

-- Connect to the database
-- \c dr_resume_db;

-- Create resumes table for US-03 Resume Upload (as per requirements)
CREATE TABLE IF NOT EXISTS resumes (
    -- Primary key using UUID for better security
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    
    -- User association (foreign key to users table from US-01)
    user_id VARCHAR(36) NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- File information (as per requirements: Store file path and metadata in DB)
    original_filename VARCHAR(255) NOT NULL,  -- User's original filename
    stored_filename VARCHAR(255) NOT NULL,    -- Secure filename for storage
    file_path VARCHAR(500) NOT NULL,          -- Full path to file (requirement)
    file_size INTEGER NOT NULL,               -- Size in bytes
    file_type VARCHAR(50) NOT NULL,           -- MIME type
    file_extension VARCHAR(10) NOT NULL,      -- .pdf, .doc, .docx (as per requirements)
    
    -- Upload metadata
    upload_status VARCHAR(20) DEFAULT 'uploaded' NOT NULL,  -- uploaded, processing, processed, error
    upload_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    
    -- Parsed content (as per requirements: "Parse file using local script")
    extracted_text TEXT,                      -- Extracted text content
    extraction_status VARCHAR(20) DEFAULT 'pending' NOT NULL,  -- pending, success, failed
    extraction_error TEXT,                    -- Error message if extraction failed
    
    -- Resume metadata
    resume_title VARCHAR(200),                -- User-provided or auto-generated title
    resume_description TEXT,                  -- User-provided description
    
    -- Processing flags
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    is_processed BOOLEAN DEFAULT FALSE NOT NULL,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    
    -- Constraints (as per requirements: PDF/DOC support)
    CONSTRAINT chk_upload_status CHECK (upload_status IN ('uploaded', 'processing', 'processed', 'error')),
    CONSTRAINT chk_extraction_status CHECK (extraction_status IN ('pending', 'success', 'failed')),
    CONSTRAINT chk_file_extension CHECK (file_extension IN ('.pdf', '.doc', '.docx')),
    CONSTRAINT chk_file_size CHECK (file_size > 0 AND file_size <= 10485760)  -- Max 10MB
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_resumes_user_id ON resumes(user_id);
CREATE INDEX IF NOT EXISTS idx_resumes_created_at ON resumes(created_at);
CREATE INDEX IF NOT EXISTS idx_resumes_is_active ON resumes(is_active);
CREATE INDEX IF NOT EXISTS idx_resumes_upload_status ON resumes(upload_status);
CREATE INDEX IF NOT EXISTS idx_resumes_extraction_status ON resumes(extraction_status);
CREATE INDEX IF NOT EXISTS idx_resumes_is_processed ON resumes(is_processed);

-- Create trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_resumes_updated_at 
    BEFORE UPDATE ON resumes 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to get user resume statistics
CREATE OR REPLACE FUNCTION get_user_resume_stats(p_user_id VARCHAR(36))
RETURNS TABLE(
    total_resumes INTEGER,
    active_resumes INTEGER,
    processed_resumes INTEGER,
    pending_extraction INTEGER,
    failed_extraction INTEGER,
    total_file_size BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::INTEGER as total_resumes,
        COUNT(CASE WHEN is_active = TRUE THEN 1 END)::INTEGER as active_resumes,
        COUNT(CASE WHEN is_processed = TRUE THEN 1 END)::INTEGER as processed_resumes,
        COUNT(CASE WHEN extraction_status = 'pending' THEN 1 END)::INTEGER as pending_extraction,
        COUNT(CASE WHEN extraction_status = 'failed' THEN 1 END)::INTEGER as failed_extraction,
        COALESCE(SUM(CASE WHEN is_active = TRUE THEN file_size ELSE 0 END), 0) as total_file_size
    FROM resumes 
    WHERE user_id = p_user_id;
END;
$$ LANGUAGE plpgsql;

-- Create function to clean up old inactive resumes
CREATE OR REPLACE FUNCTION cleanup_old_resumes(days_old INTEGER DEFAULT 90)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete resumes that are inactive and older than specified days
    DELETE FROM resumes 
    WHERE is_active = FALSE 
      AND created_at < CURRENT_TIMESTAMP - INTERVAL '1 day' * days_old;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to update resume processing status
CREATE OR REPLACE FUNCTION update_resume_processing_status(
    p_resume_id VARCHAR(36),
    p_status VARCHAR(20),
    p_extracted_text TEXT DEFAULT NULL,
    p_error_message TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE resumes 
    SET 
        extraction_status = p_status,
        extracted_text = COALESCE(p_extracted_text, extracted_text),
        extraction_error = p_error_message,
        updated_at = CURRENT_TIMESTAMP,
        is_processed = CASE WHEN p_status = 'success' THEN TRUE ELSE is_processed END
    WHERE id = p_resume_id;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- Create view for resume summary with user information
CREATE OR REPLACE VIEW resume_summary AS
SELECT 
    r.id,
    r.user_id,
    u.email as user_email,
    u.first_name,
    u.last_name,
    r.original_filename,
    r.file_size,
    r.file_extension,
    r.upload_status,
    r.extraction_status,
    r.resume_title,
    r.is_active,
    r.is_processed,
    r.created_at,
    r.updated_at
FROM resumes r
JOIN users u ON r.user_id = u.id
WHERE r.is_active = TRUE
ORDER BY r.created_at DESC;

-- Create view for file storage statistics
CREATE OR REPLACE VIEW file_storage_stats AS
SELECT 
    COUNT(*) as total_files,
    COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_files,
    SUM(file_size) as total_storage_bytes,
    SUM(CASE WHEN is_active = TRUE THEN file_size ELSE 0 END) as active_storage_bytes,
    AVG(file_size) as avg_file_size,
    COUNT(CASE WHEN file_extension = '.pdf' THEN 1 END) as pdf_count,
    COUNT(CASE WHEN file_extension = '.doc' THEN 1 END) as doc_count,
    COUNT(CASE WHEN file_extension = '.docx' THEN 1 END) as docx_count,
    COUNT(CASE WHEN extraction_status = 'success' THEN 1 END) as successful_extractions,
    COUNT(CASE WHEN extraction_status = 'failed' THEN 1 END) as failed_extractions,
    COUNT(CASE WHEN extraction_status = 'pending' THEN 1 END) as pending_extractions
FROM resumes;

-- Insert sample data for testing (optional)
-- Uncomment the following lines if you want sample data

/*
-- Sample resume data (requires existing user)
INSERT INTO resumes (
    user_id, 
    original_filename, 
    stored_filename, 
    file_path, 
    file_size, 
    file_type, 
    file_extension,
    resume_title,
    resume_description,
    extraction_status,
    extracted_text
) 
SELECT 
    id,
    'sample_resume.pdf',
    'sample_resume_' || id || '_20240101_12345.pdf',
    '/uploads/resumes/sample_resume_' || id || '_20240101_12345.pdf',
    1024000,  -- 1MB
    'application/pdf',
    '.pdf',
    'Software Engineer Resume',
    'My latest resume for software engineering positions',
    'success',
    'John Doe\nSoftware Engineer\n\nExperience:\n- 5 years in web development\n- Python, JavaScript, React\n\nEducation:\n- BS Computer Science'
FROM users 
WHERE email = '<EMAIL>'
LIMIT 1;
*/

-- Display table structure
\d resumes;

-- Show indexes
\di resumes*;

-- Display some statistics
SELECT 
    'Resumes' as table_name,
    COUNT(*) as record_count
FROM resumes
UNION ALL
SELECT 
    'Active Resumes' as table_name,
    COUNT(*) as record_count
FROM resumes
WHERE is_active = TRUE;

-- Show file storage statistics
SELECT * FROM file_storage_stats;

-- Show resume summary
SELECT * FROM resume_summary LIMIT 5;
