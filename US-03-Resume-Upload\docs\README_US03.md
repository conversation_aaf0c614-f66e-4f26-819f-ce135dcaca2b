# US-03: Resume Upload - Complete Implementation Guide

## 📋 Overview

**US-03: Resume Upload** implements file upload functionality exactly as specified in the requirements guide. This user story enables users to upload resume files to the local file system with automatic text extraction and metadata storage.

### 🎯 Requirements Compliance

**✅ EXACT IMPLEMENTATION AS PER REQUIREMENTS:**

- **Backend**: `POST /api/upload_resume` (exact endpoint as specified)
- **Local Storage**: Save resume file to local file system (`/uploads/resumes/`)
- **File Processing**: Parse file using local script (PyPDF2, python-docx)
- **Database**: Store file path and metadata in DB (SQLAlchemy)
- **Frontend**: File Upload input (accept PDF/DOC), Upload button with loading spinner
- **Tech Stack**: Flask, local storage (os, werkzeug), SQLAlchemy

## 🏗️ Architecture Overview

```
US-03-Resume-Upload/
├── backend/                      # Flask API with file upload
│   ├── us03_resume_model.py     # Resume database model
│   ├── us03_file_utils.py       # File processing utilities (local script)
│   ├── us03_upload_routes.py    # Upload API routes
│   ├── us03_app.py              # Main Flask app
│   └── requirements.txt         # Dependencies
├── frontend/                    # Upload UI (as per requirements)
│   ├── us03_upload.html         # File upload input + button + spinner
│   └── us03_upload.js           # Upload functionality
├── uploads/                     # Local file storage (as per requirements)
│   └── resumes/                 # Resume files directory
├── database/                    # Database schema
│   └── us03_resume_schema.sql   # Resume table with file paths
├── tests/                       # Upload tests
│   └── test_us03_upload.py      # Comprehensive test suite
└── docs/                        # Documentation
    └── README_US03.md           # This file
```

## 🔄 File Upload Flow (As Per Requirements)

### 1. **Upload Process**
```
User → Select File (PDF/DOC) → Upload Button → Local Storage → Parse File → Store Metadata
```

### 2. **File Processing Pipeline**
- **Validation**: File type (PDF/DOC), size, security checks
- **Storage**: Save to local file system (`/uploads/resumes/`)
- **Parsing**: Extract text using local script (PyPDF2/python-docx)
- **Database**: Store file path and metadata in DB

### 3. **API Endpoint (Exact as Required)**
```
POST /api/upload_resume
```

## 🚀 Quick Start Guide

### Prerequisites

1. **Complete US-01 and US-02** setup
2. **Python packages**: PyPDF2, python-docx for file parsing
3. **Local storage**: Directory with write permissions

### Step 1: Install Dependencies

```bash
cd US-03-Resume-Upload/backend
pip install -r requirements.txt
```

**Key dependencies (as per requirements):**
- `PyPDF2==3.0.1` - PDF text extraction (local script)
- `python-docx==0.8.11` - DOCX text extraction (local script)
- `Flask` - Web framework
- `SQLAlchemy` - Database ORM

### Step 2: Database Setup

```bash
cd US-03-Resume-Upload/database
psql -U dr_resume_user -d dr_resume_db -f us03_resume_schema.sql
```

### Step 3: Configure Environment

Create `.env` file in backend directory:
```env
# Database (PostgreSQL as per requirements)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=dr_resume_db
DB_USER=dr_resume_user
DB_PASSWORD=your_secure_password

# Security Keys (from US-02)
SECRET_KEY=your-super-secret-key-change-in-production
JWT_SECRET_KEY=your-jwt-secret-key-different-from-secret-key

# File Upload Settings (as per requirements: local storage)
UPLOAD_FOLDER=../uploads/resumes
MAX_CONTENT_LENGTH=10485760  # 10MB
```

### Step 4: Start Backend Server

```bash
cd US-03-Resume-Upload/backend
python us03_app.py
```

Server starts at: `http://localhost:5002`

### Step 5: Start Frontend Server

```bash
cd US-03-Resume-Upload/frontend
python -m http.server 3002
```

Frontend available at: `http://localhost:3002/us03_upload.html`

## 🧪 Testing

### Run Backend Tests

```bash
cd US-03-Resume-Upload/tests
pytest test_us03_upload.py -v
```

### Manual Testing Checklist

1. **File Upload (as per requirements)**:
   - [ ] Upload PDF resume
   - [ ] Upload DOC/DOCX resume
   - [ ] Test file size validation (max 10MB)
   - [ ] Test invalid file types

2. **Local Storage (as per requirements)**:
   - [ ] Verify files saved to `/uploads/resumes/`
   - [ ] Check secure filename generation
   - [ ] Verify file path storage in database

3. **Text Extraction (as per requirements: local script)**:
   - [ ] Verify PDF text extraction
   - [ ] Verify DOCX text extraction
   - [ ] Check extraction status updates

### API Testing

```bash
# Login first to get JWT token
curl -X POST http://localhost:5002/api/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password123"}'

# Upload resume (as per requirements: POST /api/upload_resume)
curl -X POST http://localhost:5002/api/upload_resume \
  -H "Authorization: Bearer TOKEN" \
  -F "file=@/path/to/resume.pdf" \
  -F "title=My Resume" \
  -F "description=Latest version"

# Get user resumes
curl -X GET http://localhost:5002/api/resumes \
  -H "Authorization: Bearer TOKEN"
```

## 📚 Learning Guide for Beginners

### Understanding File Upload (As Per Requirements)

#### 1. **Frontend File Upload Input**
```html
<!-- As per requirements: File Upload input (accept PDF/DOC) -->
<input type="file" accept=".pdf,.doc,.docx" />

<!-- As per requirements: Upload button -->
<button type="submit">Upload Resume</button>

<!-- As per requirements: loading spinner -->
<div class="loading-spinner">
    <i class="fas fa-spinner fa-spin"></i>
</div>
```

#### 2. **Backend File Handling**
```python
# As per requirements: POST /api/upload_resume
@app.route('/api/upload_resume', methods=['POST'])
@jwt_required()
def upload_resume():
    file = request.files['file']
    
    # Save to local file system (as per requirements)
    file_path = os.path.join('/uploads/resumes/', secure_filename)
    file.save(file_path)
    
    # Store file path and metadata in DB (as per requirements)
    resume = Resume(file_path=file_path, ...)
    db.session.add(resume)
    db.session.commit()
```

#### 3. **Text Extraction (Local Script)**
```python
# As per requirements: Parse file using local script
def extract_text(file_path, file_extension):
    if file_extension == '.pdf':
        # PDF extraction using PyPDF2
        with open(file_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            text = ""
            for page in reader.pages:
                text += page.extract_text()
    
    elif file_extension == '.docx':
        # DOCX extraction using python-docx
        doc = docx.Document(file_path)
        text = ""
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"
    
    return text
```

### Key Concepts Explained

#### 1. **Local File Storage (As Per Requirements)**
```python
# Save resume file to local file system (e.g., /uploads/resumes/)
UPLOAD_FOLDER = '/uploads/resumes/'

def save_file(file_obj, user_id):
    # Generate secure filename
    filename = f"{user_id}_{timestamp}_{uuid}.{ext}"
    file_path = os.path.join(UPLOAD_FOLDER, filename)
    
    # Save to local file system
    file_obj.save(file_path)
    
    return file_path
```

#### 2. **Database Metadata Storage (As Per Requirements)**
```sql
-- Store file path and metadata in DB
CREATE TABLE resumes (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) REFERENCES users(id),
    original_filename VARCHAR(255),
    file_path VARCHAR(500),          -- File path storage (requirement)
    file_size INTEGER,
    file_type VARCHAR(50),
    extracted_text TEXT              -- Parsed content
);
```

#### 3. **File Validation (PDF/DOC Support)**
```python
# As per requirements: accept PDF/DOC
ALLOWED_EXTENSIONS = {'.pdf', '.doc', '.docx'}
ALLOWED_MIME_TYPES = {
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
}

def is_allowed_file(filename):
    _, ext = os.path.splitext(filename.lower())
    return ext in ALLOWED_EXTENSIONS
```

## 🔧 Configuration Reference

### File Upload Settings

| Setting | Description | Default | Requirement |
|---------|-------------|---------|-------------|
| `UPLOAD_FOLDER` | Local storage directory | `/uploads/resumes/` | ✅ Required |
| `MAX_CONTENT_LENGTH` | Maximum upload size | 10MB | ✅ Specified |
| `ALLOWED_EXTENSIONS` | Supported file types | `.pdf`, `.doc`, `.docx` | ✅ PDF/DOC |

### API Endpoints

| Method | Endpoint | Description | Requirement |
|--------|----------|-------------|-------------|
| `POST` | `/api/upload_resume` | Upload resume file | ✅ Exact match |
| `GET` | `/api/resumes` | Get user's resumes | Additional |
| `GET` | `/api/resumes/<id>` | Get resume details | Additional |
| `GET` | `/api/resumes/<id>/download` | Download resume | Additional |

## 🔒 Security Features

### 1. **File Validation**
- File type verification (PDF/DOC only as required)
- File size limits (10MB maximum)
- Secure filename generation
- Path traversal prevention

### 2. **Access Control**
- JWT authentication required
- User-specific file access
- File ownership verification

### 3. **Storage Security**
- Files stored in dedicated directory
- Unique filenames prevent conflicts
- Soft delete for data recovery

## 🐛 Troubleshooting

### Common Issues

1. **Upload Fails with 413 Error**:
   ```
   Error: Payload Too Large
   ```
   **Solution**: Check file size (max 10MB) and server configuration

2. **Text Extraction Fails**:
   ```
   Error: PDF text extraction failed
   ```
   **Solution**: Ensure PyPDF2 is installed and PDF is not password-protected

3. **File Not Found Error**:
   ```
   Error: Resume file not found on disk
   ```
   **Solution**: Check upload directory permissions and file paths

## 🔄 Integration with Previous US

US-03 seamlessly integrates with previous user stories:

- **Uses US-01 User model** for user association
- **Extends US-02 JWT auth** for secure file access
- **Maintains compatibility** with existing authentication
- **Shares database** and user management

## 🎯 Requirements Verification

### ✅ **Backend Requirements Met**
- ✅ `POST /api/upload_resume` endpoint (exact match)
- ✅ Save resume file to local file system (`/uploads/resumes/`)
- ✅ Parse file using local script (PyPDF2, python-docx)
- ✅ Store file path and metadata in DB (SQLAlchemy)

### ✅ **Frontend Requirements Met**
- ✅ File Upload input (accept PDF/DOC)
- ✅ Upload button with loading spinner
- ✅ Show upload success/error

### ✅ **Tech Stack Requirements Met**
- ✅ Flask framework
- ✅ Local storage (os, werkzeug)
- ✅ SQLAlchemy for database

## 🔄 Next Steps

After completing US-03, you're ready for:

- **US-04**: Job Description Upload
- **US-05**: Keyword Parsing with NLP
- **US-06**: Resume-Job Matching Score
- **US-07**: AI-Powered Suggestions

---

**🎉 Congratulations!** You've successfully implemented US-03: Resume Upload exactly as per requirements. Your application now has complete file upload capabilities with local storage and text extraction!
