<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dr. Resume - Upload Resume (US-03)</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            color: white !important;
        }
        
        .nav-link {
            color: rgba(255,255,255,0.9) !important;
        }
        
        .nav-link:hover {
            color: white !important;
        }
        
        .upload-container {
            padding: 30px 0;
        }
        
        .upload-card {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }
        
        /* File Upload Input (as per requirements: File Upload input) */
        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 60px 20px;
            text-align: center;
            background: #f8f9ff;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #5a67d8;
            background: #f0f4ff;
        }
        
        .upload-area.dragover {
            border-color: #4c51bf;
            background: #e6fffa;
            transform: scale(1.02);
        }
        
        .upload-icon {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 20px;
        }
        
        .upload-text {
            font-size: 1.2rem;
            color: #4a5568;
            margin-bottom: 10px;
        }
        
        .upload-subtext {
            color: #718096;
            font-size: 0.9rem;
        }
        
        .file-input {
            display: none;
        }
        
        .selected-file {
            background: #e6fffa;
            border: 2px solid #38b2ac;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .file-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .file-details {
            display: flex;
            align-items: center;
        }
        
        .file-icon {
            font-size: 2rem;
            color: #38b2ac;
            margin-right: 15px;
        }
        
        /* Upload Button (as per requirements: Upload button) */
        .btn-upload {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 12px 30px;
            font-size: 16px;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
        }
        
        .btn-upload:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-upload:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: none;
        }
        
        /* Loading Spinner (as per requirements: loading spinner) */
        .loading-spinner {
            display: none;
        }
        
        .progress-container {
            margin-top: 20px;
            display: none;
        }
        
        .alert {
            border-radius: 8px;
            border: none;
            margin-bottom: 20px;
        }
        
        .resume-list {
            margin-top: 40px;
        }
        
        .resume-item {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            border: 1px solid #e9ecef;
        }
        
        .resume-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .resume-title {
            font-weight: 600;
            color: #2d3748;
            margin: 0;
        }
        
        .resume-meta {
            font-size: 0.9rem;
            color: #718096;
        }
        
        .resume-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn-sm {
            padding: 5px 12px;
            font-size: 0.875rem;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .status-success {
            background: #c6f6d5;
            color: #22543d;
        }
        
        .status-pending {
            background: #fef5e7;
            color: #744210;
        }
        
        .status-error {
            background: #fed7d7;
            color: #742a2a;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-label {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
        }
        
        .form-control {
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 12px 15px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-stethoscope me-2"></i>
                Dr. Resume - US-03
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#" id="userEmail">Loading...</a>
                <button class="btn btn-outline-light btn-sm ms-2" onclick="logout()">
                    <i class="fas fa-sign-out-alt me-1"></i>
                    Logout
                </button>
            </div>
        </div>
    </nav>
    
    <!-- Upload Content -->
    <div class="upload-container">
        <div class="container">
            <!-- Alert Messages -->
            <div id="alertContainer"></div>
            
            <!-- Upload Card -->
            <div class="upload-card">
                <h2 class="text-center mb-4">
                    <i class="fas fa-cloud-upload-alt me-2"></i>
                    Upload Your Resume
                </h2>
                <p class="text-center text-muted mb-4">
                    <strong>US-03:</strong> Resume Upload with Local Storage<br>
                    <small>Supported formats: PDF, DOC, DOCX (Max 10MB)</small>
                </p>
                
                <!-- File Upload Input (as per requirements) -->
                <div class="upload-area" id="uploadArea" onclick="document.getElementById('fileInput').click()">
                    <div class="upload-icon">
                        <i class="fas fa-file-upload"></i>
                    </div>
                    <div class="upload-text">
                        Click to browse or drag and drop your resume
                    </div>
                    <div class="upload-subtext">
                        Supported formats: PDF, DOC, DOCX (Max 10MB)
                    </div>
                </div>
                
                <!-- Hidden File Input -->
                <input type="file" id="fileInput" class="file-input" accept=".pdf,.doc,.docx" />
                
                <!-- Selected File Display -->
                <div id="selectedFileContainer" style="display: none;">
                    <div class="selected-file">
                        <div class="file-info">
                            <div class="file-details">
                                <div class="file-icon">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <div>
                                    <div id="fileName" class="fw-bold"></div>
                                    <div id="fileSize" class="text-muted small"></div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="clearSelectedFile()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Upload Form -->
                <form id="uploadForm" style="display: none;">
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="resumeTitle" class="form-label">
                                    <i class="fas fa-heading me-1"></i>
                                    Resume Title (Optional)
                                </label>
                                <input 
                                    type="text" 
                                    class="form-control" 
                                    id="resumeTitle" 
                                    name="title"
                                    placeholder="e.g., Software Engineer Resume"
                                >
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="resumeDescription" class="form-label">
                                    <i class="fas fa-align-left me-1"></i>
                                    Description (Optional)
                                </label>
                                <textarea 
                                    class="form-control" 
                                    id="resumeDescription" 
                                    name="description"
                                    rows="3"
                                    placeholder="Brief description of this resume version"
                                ></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Upload Button (as per requirements) -->
                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-upload" id="uploadBtn">
                            <span class="loading-spinner">
                                <i class="fas fa-spinner fa-spin me-2"></i>
                            </span>
                            <span class="btn-text">
                                <i class="fas fa-upload me-2"></i>
                                Upload Resume
                            </span>
                        </button>
                    </div>
                </form>
                
                <!-- Progress Bar (as per requirements: loading spinner) -->
                <div class="progress-container" id="progressContainer">
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%" id="progressBar">
                        </div>
                    </div>
                    <div class="text-center mt-2">
                        <small id="progressText">Uploading...</small>
                    </div>
                </div>
            </div>
            
            <!-- Resume List -->
            <div class="resume-list">
                <h3><i class="fas fa-list me-2"></i>Your Uploaded Resumes</h3>
                <div id="resumeListContainer">
                    <div class="text-center py-4">
                        <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                        <p class="text-muted mt-2">Loading your resumes...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Login JS for auth utilities (from US-02) -->
    <script src="../../US-02-Login-JWT-Token/frontend/us02_login.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="us03_upload.js"></script>
</body>
</html>
