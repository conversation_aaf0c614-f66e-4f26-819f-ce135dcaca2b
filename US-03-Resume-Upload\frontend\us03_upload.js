/**
 * US-03: Resume Upload - Frontend JavaScript
 * =========================================
 * 
 * This file handles the frontend logic for resume upload as per requirements:
 * - File Upload input (accept PDF/DOC)
 * - Upload button with loading spinner
 * - Show upload success/error
 * 
 * Tech Stack: Vanilla JavaScript, Bootstrap, Fetch API, FormData
 */

// Configuration (as per requirements: POST /api/upload_resume)
const CONFIG = {
    API_BASE_URL: 'http://localhost:5002/api',
    ENDPOINTS: {
        UPLOAD_RESUME: '/upload_resume',  // As per requirements: POST /api/upload_resume
        GET_RESUMES: '/resumes',
        GET_RESUME_DETAILS: '/resumes',
        DOWNLOAD_RESUME: '/resumes',
        UPDATE_RESUME: '/resumes',
        DELETE_RESUME: '/resumes',
        REPROCESS_RESUME: '/resumes'
    },
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_TYPES: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    ALLOWED_EXTENSIONS: ['.pdf', '.doc', '.docx']  // As per requirements: PDF/DOC
};

// Global variables
let selectedFile = null;
let userResumes = [];

// DOM Elements
let elements = {};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    checkAuthentication();
    initializeElements();
    setupEventListeners();
    loadUserData();
    loadUserResumes();
    console.log('✅ US-03 Upload page initialized');
});

/**
 * Check if user is authenticated (requires JWT from US-02)
 */
function checkAuthentication() {
    if (!window.DrResumeAuth || !window.DrResumeAuth.isLoggedIn()) {
        alert('You must be logged in to upload resumes.');
        window.location.href = '../../US-02-Login-JWT-Token/frontend/us02_login.html';
        return;
    }
}

/**
 * Initialize DOM element references
 */
function initializeElements() {
    elements = {
        uploadArea: document.getElementById('uploadArea'),
        fileInput: document.getElementById('fileInput'),
        selectedFileContainer: document.getElementById('selectedFileContainer'),
        fileName: document.getElementById('fileName'),
        fileSize: document.getElementById('fileSize'),
        uploadForm: document.getElementById('uploadForm'),
        uploadBtn: document.getElementById('uploadBtn'),
        progressContainer: document.getElementById('progressContainer'),
        progressBar: document.getElementById('progressBar'),
        progressText: document.getElementById('progressText'),
        alertContainer: document.getElementById('alertContainer'),
        resumeListContainer: document.getElementById('resumeListContainer'),
        userEmail: document.getElementById('userEmail')
    };
}

/**
 * Setup event listeners
 */
function setupEventListeners() {
    // File input change
    elements.fileInput.addEventListener('change', handleFileSelect);
    
    // Form submission
    elements.uploadForm.addEventListener('submit', handleUpload);
    
    // Drag and drop events
    elements.uploadArea.addEventListener('dragover', handleDragOver);
    elements.uploadArea.addEventListener('dragleave', handleDragLeave);
    elements.uploadArea.addEventListener('drop', handleDrop);
    
    // Prevent default drag behaviors on document
    document.addEventListener('dragover', (e) => e.preventDefault());
    document.addEventListener('drop', (e) => e.preventDefault());
}

/**
 * Load user data
 */
function loadUserData() {
    const userData = window.DrResumeAuth.getUserData();
    if (userData) {
        elements.userEmail.textContent = userData.email;
    }
}

/**
 * Handle file selection (as per requirements: File Upload input)
 */
function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        validateAndSetFile(file);
    }
}

/**
 * Handle drag over
 */
function handleDragOver(event) {
    event.preventDefault();
    elements.uploadArea.classList.add('dragover');
}

/**
 * Handle drag leave
 */
function handleDragLeave(event) {
    event.preventDefault();
    elements.uploadArea.classList.remove('dragover');
}

/**
 * Handle file drop
 */
function handleDrop(event) {
    event.preventDefault();
    elements.uploadArea.classList.remove('dragover');
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
        validateAndSetFile(files[0]);
    }
}

/**
 * Validate and set selected file (as per requirements: accept PDF/DOC)
 */
function validateAndSetFile(file) {
    // Validate file type (as per requirements: PDF/DOC)
    if (!CONFIG.ALLOWED_TYPES.includes(file.type)) {
        showAlert('Invalid file type. Please select a PDF, DOC, or DOCX file.', 'danger');
        return;
    }
    
    // Validate file size
    if (file.size > CONFIG.MAX_FILE_SIZE) {
        const maxMB = CONFIG.MAX_FILE_SIZE / (1024 * 1024);
        showAlert(`File too large. Maximum size is ${maxMB}MB.`, 'danger');
        return;
    }
    
    // Validate file extension
    const fileName = file.name.toLowerCase();
    const hasValidExtension = CONFIG.ALLOWED_EXTENSIONS.some(ext => fileName.endsWith(ext));
    if (!hasValidExtension) {
        showAlert('Invalid file extension. Please select a PDF, DOC, or DOCX file.', 'danger');
        return;
    }
    
    // Set selected file
    selectedFile = file;
    displaySelectedFile(file);
}

/**
 * Display selected file information
 */
function displaySelectedFile(file) {
    elements.fileName.textContent = file.name;
    elements.fileSize.textContent = formatFileSize(file.size);
    
    elements.selectedFileContainer.style.display = 'block';
    elements.uploadForm.style.display = 'block';
    elements.uploadArea.style.display = 'none';
    
    // Auto-fill title if empty
    const titleInput = document.getElementById('resumeTitle');
    if (!titleInput.value) {
        titleInput.value = file.name.replace(/\.[^/.]+$/, ""); // Remove extension
    }
}

/**
 * Clear selected file
 */
function clearSelectedFile() {
    selectedFile = null;
    elements.fileInput.value = '';
    elements.selectedFileContainer.style.display = 'none';
    elements.uploadForm.style.display = 'none';
    elements.uploadArea.style.display = 'block';
    elements.progressContainer.style.display = 'none';
}

/**
 * Handle file upload (as per requirements: Upload button with loading spinner)
 */
async function handleUpload(event) {
    event.preventDefault();
    
    if (!selectedFile) {
        showAlert('Please select a file to upload.', 'danger');
        return;
    }
    
    // Get form data
    const formData = new FormData();
    formData.append('file', selectedFile);
    
    const title = document.getElementById('resumeTitle').value.trim();
    const description = document.getElementById('resumeDescription').value.trim();
    
    if (title) formData.append('title', title);
    if (description) formData.append('description', description);
    
    // Show loading state (as per requirements: loading spinner)
    setUploadingState(true);
    
    try {
        const accessToken = window.DrResumeAuth.getAccessToken();
        
        // Call upload endpoint (as per requirements: POST /api/upload_resume)
        const response = await fetch(`${CONFIG.API_BASE_URL}${CONFIG.ENDPOINTS.UPLOAD_RESUME}`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${accessToken}`
            },
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            // Show upload success (as per requirements)
            showAlert('Resume uploaded successfully! Text extraction in progress...', 'success');
            
            // Reset form
            clearSelectedFile();
            document.getElementById('resumeTitle').value = '';
            document.getElementById('resumeDescription').value = '';
            
            // Reload resume list
            await loadUserResumes();
            
        } else {
            // Show upload error (as per requirements)
            showAlert(result.message || 'Upload failed. Please try again.', 'danger');
        }
        
    } catch (error) {
        console.error('Upload error:', error);
        showAlert('Network error. Please check your connection and try again.', 'danger');
    } finally {
        setUploadingState(false);
    }
}

/**
 * Set uploading state (as per requirements: loading spinner)
 */
function setUploadingState(isUploading) {
    elements.uploadBtn.disabled = isUploading;
    
    const spinner = elements.uploadBtn.querySelector('.loading-spinner');
    const text = elements.uploadBtn.querySelector('.btn-text');
    
    if (isUploading) {
        spinner.style.display = 'inline';
        text.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Uploading...';
        elements.progressContainer.style.display = 'block';
        
        // Simulate progress
        simulateProgress();
    } else {
        spinner.style.display = 'none';
        text.innerHTML = '<i class="fas fa-upload me-2"></i>Upload Resume';
        elements.progressContainer.style.display = 'none';
    }
}

/**
 * Simulate upload progress
 */
function simulateProgress() {
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 90) progress = 90;
        
        elements.progressBar.style.width = `${progress}%`;
        elements.progressText.textContent = `Uploading... ${Math.round(progress)}%`;
        
        if (progress >= 90) {
            clearInterval(interval);
            elements.progressText.textContent = 'Processing file...';
        }
    }, 200);
}

/**
 * Load user resumes
 */
async function loadUserResumes() {
    try {
        const accessToken = window.DrResumeAuth.getAccessToken();
        
        const response = await fetch(`${CONFIG.API_BASE_URL}${CONFIG.ENDPOINTS.GET_RESUMES}`, {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            userResumes = result.resumes;
            displayResumeList(userResumes);
        } else {
            showAlert('Failed to load resumes: ' + result.message, 'danger');
        }
        
    } catch (error) {
        console.error('Error loading resumes:', error);
        showAlert('Failed to load resumes. Please refresh the page.', 'danger');
    }
}

/**
 * Display resume list
 */
function displayResumeList(resumes) {
    if (resumes.length === 0) {
        elements.resumeListContainer.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                <p class="text-muted">No resumes uploaded yet. Upload your first resume above!</p>
            </div>
        `;
        return;
    }
    
    const resumeHTML = resumes.map(resume => `
        <div class="resume-item">
            <div class="resume-header">
                <div>
                    <h5 class="resume-title">${escapeHtml(resume.resume_title)}</h5>
                    <div class="resume-meta">
                        <i class="fas fa-file me-1"></i>
                        ${escapeHtml(resume.original_filename)} 
                        (${formatFileSize(resume.file_size)})
                        <span class="ms-2">
                            <i class="fas fa-calendar me-1"></i>
                            ${formatDate(resume.created_at)}
                        </span>
                        <span class="status-badge ${getStatusClass(resume.extraction_status)} ms-2">
                            ${getStatusText(resume.extraction_status)}
                        </span>
                    </div>
                </div>
                <div class="resume-actions">
                    <button class="btn btn-outline-primary btn-sm" onclick="viewResume('${resume.id}')">
                        <i class="fas fa-eye"></i> View
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="downloadResume('${resume.id}')">
                        <i class="fas fa-download"></i> Download
                    </button>
                    <button class="btn btn-outline-danger btn-sm" onclick="deleteResume('${resume.id}')">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            </div>
            ${resume.resume_description ? `<p class="text-muted mb-0">${escapeHtml(resume.resume_description)}</p>` : ''}
        </div>
    `).join('');
    
    elements.resumeListContainer.innerHTML = resumeHTML;
}

/**
 * Utility functions
 */

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
}

function getStatusClass(status) {
    switch (status) {
        case 'success': return 'status-success';
        case 'failed': return 'status-error';
        default: return 'status-pending';
    }
}

function getStatusText(status) {
    switch (status) {
        case 'success': return 'Extracted';
        case 'failed': return 'Failed';
        case 'pending': return 'Processing';
        default: return 'Unknown';
    }
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function showAlert(message, type = 'info') {
    const alertHTML = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${getAlertIcon(type)} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    elements.alertContainer.innerHTML = alertHTML;

    // Auto-dismiss success alerts
    if (type === 'success') {
        setTimeout(() => {
            const alert = elements.alertContainer.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }
}

function getAlertIcon(type) {
    const icons = {
        success: 'check-circle',
        danger: 'exclamation-triangle',
        warning: 'exclamation-circle',
        info: 'info-circle'
    };
    return icons[type] || 'info-circle';
}

/**
 * Resume management functions
 */

async function viewResume(resumeId) {
    try {
        const accessToken = window.DrResumeAuth.getAccessToken();

        const response = await fetch(`${CONFIG.API_BASE_URL}${CONFIG.ENDPOINTS.GET_RESUME_DETAILS}/${resumeId}`, {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        const result = await response.json();

        if (result.success) {
            showResumeModal(result.resume);
        } else {
            showAlert('Failed to load resume details: ' + result.message, 'danger');
        }

    } catch (error) {
        console.error('Error loading resume details:', error);
        showAlert('Failed to load resume details.', 'danger');
    }
}

async function downloadResume(resumeId) {
    try {
        const accessToken = window.DrResumeAuth.getAccessToken();

        const response = await fetch(`${CONFIG.API_BASE_URL}${CONFIG.ENDPOINTS.DOWNLOAD_RESUME}/${resumeId}/download`, {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = response.headers.get('Content-Disposition')?.split('filename=')[1] || 'resume';
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } else {
            const result = await response.json();
            showAlert('Download failed: ' + result.message, 'danger');
        }

    } catch (error) {
        console.error('Download error:', error);
        showAlert('Download failed. Please try again.', 'danger');
    }
}

async function deleteResume(resumeId) {
    if (!confirm('Are you sure you want to delete this resume? This action cannot be undone.')) {
        return;
    }

    try {
        const accessToken = window.DrResumeAuth.getAccessToken();

        const response = await fetch(`${CONFIG.API_BASE_URL}${CONFIG.ENDPOINTS.DELETE_RESUME}/${resumeId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        const result = await response.json();

        if (result.success) {
            showAlert('Resume deleted successfully.', 'success');
            await loadUserResumes();
        } else {
            showAlert('Delete failed: ' + result.message, 'danger');
        }

    } catch (error) {
        console.error('Delete error:', error);
        showAlert('Delete failed. Please try again.', 'danger');
    }
}

function showResumeModal(resume) {
    const modalHTML = `
        <div class="modal fade" id="resumeModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-file-alt me-2"></i>
                            ${escapeHtml(resume.resume_title)}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <strong>Original Filename:</strong><br>
                                ${escapeHtml(resume.original_filename)}
                            </div>
                            <div class="col-md-6">
                                <strong>File Size:</strong><br>
                                ${formatFileSize(resume.file_size)}
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <strong>Upload Date:</strong><br>
                                ${formatDate(resume.created_at)}
                            </div>
                            <div class="col-md-6">
                                <strong>Text Extraction:</strong><br>
                                <span class="status-badge ${getStatusClass(resume.extraction_status)}">
                                    ${getStatusText(resume.extraction_status)}
                                </span>
                            </div>
                        </div>
                        ${resume.resume_description ? `
                            <div class="mb-3">
                                <strong>Description:</strong><br>
                                ${escapeHtml(resume.resume_description)}
                            </div>
                        ` : ''}
                        ${resume.extracted_text ? `
                            <div class="mb-3">
                                <strong>Extracted Text:</strong>
                                <div class="border rounded p-3 bg-light" style="max-height: 300px; overflow-y: auto;">
                                    <pre style="white-space: pre-wrap; font-family: inherit;">${escapeHtml(resume.extracted_text)}</pre>
                                </div>
                            </div>
                        ` : ''}
                        ${resume.extraction_error ? `
                            <div class="alert alert-danger">
                                <strong>Extraction Error:</strong><br>
                                ${escapeHtml(resume.extraction_error)}
                            </div>
                        ` : ''}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary" onclick="downloadResume('${resume.id}')">
                            <i class="fas fa-download me-1"></i> Download
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('resumeModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('resumeModal'));
    modal.show();
}

async function logout() {
    if (confirm('Are you sure you want to logout?')) {
        await window.DrResumeAuth.logout();
    }
}

// Make functions globally available
window.viewResume = viewResume;
window.downloadResume = downloadResume;
window.deleteResume = deleteResume;
window.logout = logout;
window.clearSelectedFile = clearSelectedFile;
