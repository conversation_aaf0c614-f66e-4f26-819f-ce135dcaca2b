"""
US-03: Resume Upload - Test Suite
================================

This file contains comprehensive tests for US-03 Resume Upload functionality.
As per requirements: Test file upload, local storage, text extraction, and metadata storage.

Tech Stack: pytest, Flask-Testing, File Upload Testing
"""

import pytest
import json
import sys
import os
import tempfile
from datetime import datetime
from io import BytesIO

# Add backend directories to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-01-User-Registration', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-02-Login-JWT-Token', 'backend'))

from us03_app import create_app
from us01_user_model import db, User
from us03_resume_model import Resume
from us03_file_utils import FileProcessor

class TestFileUpload:
    """Test cases for file upload functionality (as per requirements)"""
    
    @pytest.fixture
    def app(self):
        """Create test Flask application"""
        app = create_app('testing')
        app.config['TESTING'] = True
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        
        # Create temporary upload directory (as per requirements: local file system)
        app.config['UPLOAD_FOLDER'] = tempfile.mkdtemp()
        
        with app.app_context():
            db.create_all()
            
            # Create test user
            test_user = User(
                email='<EMAIL>',
                password='password123',
                first_name='Test',
                last_name='User'
            )
            db.session.add(test_user)
            db.session.commit()
            
            yield app
            db.drop_all()
    
    @pytest.fixture
    def client(self, app):
        """Create test client"""
        return app.test_client()
    
    @pytest.fixture
    def auth_headers(self, client):
        """Get authentication headers"""
        # Login to get JWT token
        response = client.post('/api/login', 
            json={
                'email': '<EMAIL>',
                'password': 'password123'
            }
        )
        
        data = json.loads(response.data)
        access_token = data['tokens']['access_token']
        
        return {'Authorization': f'Bearer {access_token}'}
    
    def create_test_pdf(self):
        """Create a test PDF file (as per requirements: PDF support)"""
        # Simple PDF content (minimal valid PDF)
        pdf_content = b"""%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
72 720 Td
(Test Resume Content) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
300
%%EOF"""
        return BytesIO(pdf_content)
    
    def test_upload_resume_success(self, client, auth_headers):
        """Test successful resume upload (as per requirements: POST /api/upload_resume)"""
        # Create test file
        test_file = self.create_test_pdf()
        
        response = client.post('/api/upload_resume',  # As per requirements
            data={
                'file': (test_file, 'test_resume.pdf', 'application/pdf'),
                'title': 'Test Resume',
                'description': 'Test resume description'
            },
            headers=auth_headers,
            content_type='multipart/form-data'
        )
        
        assert response.status_code == 201
        data = json.loads(response.data)
        assert data['success'] == True
        assert 'resume' in data
        assert data['resume']['original_filename'] == 'test_resume.pdf'
        assert data['resume']['resume_title'] == 'Test Resume'
    
    def test_upload_resume_no_file(self, client, auth_headers):
        """Test upload without file"""
        response = client.post('/api/upload_resume',
            data={},
            headers=auth_headers,
            content_type='multipart/form-data'
        )
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['success'] == False
        assert 'No file provided' in data['message']
    
    def test_upload_resume_invalid_type(self, client, auth_headers):
        """Test upload with invalid file type (as per requirements: PDF/DOC only)"""
        # Create text file instead of PDF
        test_file = BytesIO(b"This is not a PDF file")
        
        response = client.post('/api/upload_resume',
            data={
                'file': (test_file, 'test.txt', 'text/plain')
            },
            headers=auth_headers,
            content_type='multipart/form-data'
        )
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['success'] == False
        assert 'not allowed' in data['message'].lower()
    
    def test_upload_resume_no_auth(self, client):
        """Test upload without authentication (JWT required)"""
        test_file = self.create_test_pdf()
        
        response = client.post('/api/upload_resume',
            data={
                'file': (test_file, 'test_resume.pdf', 'application/pdf')
            },
            content_type='multipart/form-data'
        )
        
        assert response.status_code == 401
        data = json.loads(response.data)
        assert data['success'] == False
    
    def test_get_user_resumes(self, client, auth_headers, app):
        """Test getting user's resumes"""
        with app.app_context():
            # Create test resume in database (as per requirements: metadata storage)
            user = User.find_by_email('<EMAIL>')
            resume, _ = Resume.create_resume(
                user_id=user.id,
                original_filename='test.pdf',
                stored_filename='stored_test.pdf',
                file_path='/test/path/stored_test.pdf',
                file_size=1024,
                file_type='application/pdf',
                file_extension='.pdf'
            )
        
        response = client.get('/api/resumes', headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] == True
        assert len(data['resumes']) == 1
        assert data['resumes'][0]['original_filename'] == 'test.pdf'
    
    def test_get_resume_details(self, client, auth_headers, app):
        """Test getting resume details"""
        with app.app_context():
            # Create test resume
            user = User.find_by_email('<EMAIL>')
            resume, _ = Resume.create_resume(
                user_id=user.id,
                original_filename='test.pdf',
                stored_filename='stored_test.pdf',
                file_path='/test/path/stored_test.pdf',
                file_size=1024,
                file_type='application/pdf',
                file_extension='.pdf'
            )
            resume_id = resume.id
        
        response = client.get(f'/api/resumes/{resume_id}', headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] == True
        assert data['resume']['id'] == resume_id
    
    def test_update_resume(self, client, auth_headers, app):
        """Test updating resume metadata"""
        with app.app_context():
            # Create test resume
            user = User.find_by_email('<EMAIL>')
            resume, _ = Resume.create_resume(
                user_id=user.id,
                original_filename='test.pdf',
                stored_filename='stored_test.pdf',
                file_path='/test/path/stored_test.pdf',
                file_size=1024,
                file_type='application/pdf',
                file_extension='.pdf'
            )
            resume_id = resume.id
        
        response = client.put(f'/api/resumes/{resume_id}',
            json={
                'title': 'Updated Title',
                'description': 'Updated description'
            },
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] == True
        assert data['resume']['resume_title'] == 'Updated Title'
    
    def test_delete_resume(self, client, auth_headers, app):
        """Test deleting resume"""
        with app.app_context():
            # Create test resume
            user = User.find_by_email('<EMAIL>')
            resume, _ = Resume.create_resume(
                user_id=user.id,
                original_filename='test.pdf',
                stored_filename='stored_test.pdf',
                file_path='/test/path/stored_test.pdf',
                file_size=1024,
                file_type='application/pdf',
                file_extension='.pdf'
            )
            resume_id = resume.id
        
        response = client.delete(f'/api/resumes/{resume_id}', headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] == True

class TestFileProcessor:
    """Test cases for file processing utilities (as per requirements: local script)"""
    
    @pytest.fixture
    def file_processor(self):
        """Create file processor instance"""
        upload_folder = tempfile.mkdtemp()
        return FileProcessor(upload_folder)
    
    def test_is_allowed_file(self, file_processor):
        """Test file type validation (as per requirements: PDF/DOC)"""
        assert file_processor.is_allowed_file('test.pdf') == True
        assert file_processor.is_allowed_file('test.doc') == True
        assert file_processor.is_allowed_file('test.docx') == True
        assert file_processor.is_allowed_file('test.txt') == False
        assert file_processor.is_allowed_file('test.jpg') == False
        assert file_processor.is_allowed_file('') == False
    
    def test_generate_secure_filename(self, file_processor):
        """Test secure filename generation (as per requirements: local storage)"""
        filename = file_processor.generate_secure_filename('test.pdf', 'user123')
        
        assert filename.endswith('.pdf')
        assert 'user123' in filename
        assert len(filename) > len('test.pdf')
    
    def test_validate_file_size(self, file_processor):
        """Test file size validation"""
        # Create mock file object
        class MockFile:
            def __init__(self, size, filename):
                self.size = size
                self.filename = filename
                self.position = 0
            
            def seek(self, pos, whence=0):
                if whence == 2:  # SEEK_END
                    self.position = self.size
                else:
                    self.position = pos
            
            def tell(self):
                return self.position
        
        # Test valid file size
        small_file = MockFile(1024, 'test.pdf')  # 1KB
        is_valid, error, info = file_processor.validate_file(small_file)
        assert is_valid == True
        
        # Test file too large
        large_file = MockFile(20 * 1024 * 1024, 'test.pdf')  # 20MB
        is_valid, error, info = file_processor.validate_file(large_file)
        assert is_valid == False
        assert 'too large' in error.lower()

class TestResumeModel:
    """Test cases for Resume model (as per requirements: metadata storage)"""
    
    @pytest.fixture
    def app(self):
        """Create test Flask application"""
        app = create_app('testing')
        app.config['TESTING'] = True
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        
        with app.app_context():
            db.create_all()
            
            # Create test user
            test_user = User(
                email='<EMAIL>',
                password='password123'
            )
            db.session.add(test_user)
            db.session.commit()
            
            yield app
            db.drop_all()
    
    def test_create_resume(self, app):
        """Test resume creation (as per requirements: file path and metadata storage)"""
        with app.app_context():
            user = User.find_by_email('<EMAIL>')
            
            resume, error = Resume.create_resume(
                user_id=user.id,
                original_filename='test.pdf',
                stored_filename='stored_test.pdf',
                file_path='/test/path/stored_test.pdf',  # As per requirements: file path storage
                file_size=1024,
                file_type='application/pdf',
                file_extension='.pdf',
                resume_title='Test Resume'
            )
            
            assert resume is not None
            assert error is None
            assert resume.original_filename == 'test.pdf'
            assert resume.resume_title == 'Test Resume'
            assert resume.file_path == '/test/path/stored_test.pdf'  # File path stored
    
    def test_find_by_user(self, app):
        """Test finding resumes by user"""
        with app.app_context():
            user = User.find_by_email('<EMAIL>')
            
            # Create multiple resumes
            for i in range(3):
                Resume.create_resume(
                    user_id=user.id,
                    original_filename=f'test{i}.pdf',
                    stored_filename=f'stored_test{i}.pdf',
                    file_path=f'/test/path/stored_test{i}.pdf',
                    file_size=1024,
                    file_type='application/pdf',
                    file_extension='.pdf'
                )
            
            resumes = Resume.find_by_user(user.id)
            assert len(resumes) == 3
    
    def test_resume_to_dict(self, app):
        """Test resume serialization"""
        with app.app_context():
            user = User.find_by_email('<EMAIL>')
            
            resume, _ = Resume.create_resume(
                user_id=user.id,
                original_filename='test.pdf',
                stored_filename='stored_test.pdf',
                file_path='/test/path/stored_test.pdf',
                file_size=1024,
                file_type='application/pdf',
                file_extension='.pdf'
            )
            
            resume_dict = resume.to_dict()
            
            assert 'id' in resume_dict
            assert 'original_filename' in resume_dict
            assert 'file_size' in resume_dict
            assert 'file_path' in resume_dict  # File path included
            assert resume_dict['original_filename'] == 'test.pdf'

if __name__ == '__main__':
    pytest.main([__file__, '-v'])
