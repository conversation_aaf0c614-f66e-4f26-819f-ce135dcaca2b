# US-04: Job Description Upload - Python Dependencies
# ==================================================
# 
# This file contains all Python packages required for US-04 JD Upload feature
# Install with: pip install -r requirements.txt
# 
# Dependencies include packages from US-01, US-02, and new US-04 requirements

# Core Flask Framework
Flask==2.3.3
Werkzeug==2.3.7

# Database ORM and PostgreSQL
Flask-SQLAlchemy==3.0.5
SQLAlchemy==2.0.21
psycopg2-binary==2.9.7

# JWT Authentication (from US-02)
Flask-JWT-Extended==4.5.3
PyJWT==2.8.0

# Password Hashing (from US-01)
bcrypt==4.0.1

# CORS Support for Frontend Integration
Flask-CORS==4.0.0

# Environment Variables Management
python-dotenv==1.0.0

# Date and Time Utilities
python-dateutil==2.8.2

# JSON Handling and Validation
jsonschema==4.19.1

# Text Processing (for future keyword extraction in US-05)
# These are included for compatibility with future features
nltk==3.8.1
spacy==3.6.1

# Development and Testing Dependencies
pytest==7.4.2
pytest-flask==1.2.0
requests==2.31.0

# Production Server (optional)
gunicorn==21.2.0

# Logging and Monitoring
colorlog==6.7.0

# Security
cryptography==41.0.4

# Data Validation
marshmallow==3.20.1
email-validator==2.0.0

# Performance Monitoring (optional)
flask-limiter==3.5.0

# Configuration Management
configparser==6.0.0

# UUID Generation (built-in, but listed for clarity)
# uuid - built-in Python module

# Regular Expressions (built-in, but listed for clarity)
# re - built-in Python module

# Operating System Interface (built-in)
# os - built-in Python module

# System-specific Parameters (built-in)
# sys - built-in Python module

# Date and Time (built-in)
# datetime - built-in Python module

# JSON (built-in)
# json - built-in Python module
