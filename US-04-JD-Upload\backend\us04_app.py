"""
US-04: Job Description Upload - Main Flask Application
=====================================================

This is the main Flask application for US-04 JD Upload feature.
It integrates with previous US features and provides JD upload functionality.

Tech Stack: Flask, Flask-JWT-Extended, SQLAlchemy, PostgreSQL
Dependencies: US-01 (User Registration), US-02 (JWT Authentication), US-03 (File Upload patterns)

Usage:
    python us04_app.py

Environment Variables Required:
    - DATABASE_URL or individual DB connection parameters
    - JWT_SECRET_KEY
    - See README_US04.md for complete setup instructions
"""

import os
import sys
from datetime import datetime, timedelta
from flask import Flask, jsonify
from flask_jwt_extended import JWTManager
from flask_cors import CORS

# Add paths for imports from previous US
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-01-User-Registration', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-02-Login-JWT-Token', 'backend'))

try:
    from us01_user_model import db, User
    from us02_auth_routes import auth_bp
    from us02_auth_routes import (
        check_if_token_revoked, jwt_additional_claims_loader,
        expired_token_callback, invalid_token_callback, missing_token_callback
    )
    print("✅ Successfully imported from US-01 and US-02")
except ImportError as e:
    print(f"⚠️  Import warning: {e}")
    print("Some features may not work without US-01 and US-02")
    db = None
    auth_bp = None

# Import US-04 components
try:
    from us04_jd_model import JobDescription
    from us04_upload_routes import jd_bp
    print("✅ Successfully imported US-04 components")
except ImportError as e:
    print(f"❌ Error importing US-04 components: {e}")
    sys.exit(1)

def create_app():
    """
    Create and configure the Flask application for US-04
    
    Returns:
        Flask: Configured Flask application
    """
    app = Flask(__name__)
    
    # Configuration
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production')
    
    # Database configuration
    app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get(
        'DATABASE_URL',
        'postgresql://username:password@localhost/dr_resume_db'
    )
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
    }
    
    # JWT Configuration (from US-02)
    app.config['JWT_SECRET_KEY'] = os.environ.get('JWT_SECRET_KEY', 'jwt-secret-string-change-in-production')
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=1)
    app.config['JWT_REFRESH_TOKEN_EXPIRES'] = timedelta(days=30)
    app.config['JWT_BLACKLIST_ENABLED'] = True
    app.config['JWT_BLACKLIST_TOKEN_CHECKS'] = ['access', 'refresh']
    
    # Initialize extensions
    if db:
        db.init_app(app)
    
    # Initialize JWT
    jwt = JWTManager(app)
    
    # JWT Configuration (from US-02)
    if 'check_if_token_revoked' in globals():
        jwt.token_in_blocklist_loader(check_if_token_revoked)
        jwt.additional_claims_loader(jwt_additional_claims_loader)
        jwt.expired_token_loader(expired_token_callback)
        jwt.invalid_token_loader(invalid_token_callback)
        jwt.unauthorized_loader(missing_token_callback)
    
    # Enable CORS for frontend integration
    CORS(app, origins=['http://localhost:3000', 'http://127.0.0.1:3000'])
    
    # Register blueprints
    if auth_bp:
        app.register_blueprint(auth_bp)
        print("✅ Registered authentication blueprint from US-02")
    
    app.register_blueprint(jd_bp)
    print("✅ Registered JD upload blueprint")
    
    # Root endpoint
    @app.route('/')
    def home():
        """Home endpoint with API information"""
        return jsonify({
            'success': True,
            'message': 'Dr. Resume API - US-04: Job Description Upload',
            'version': '1.0.0',
            'features': {
                'us01_registration': 'active' if auth_bp else 'not_available',
                'us02_jwt_auth': 'active' if auth_bp else 'not_available',
                'us04_jd_upload': 'active'
            },
            'endpoints': {
                'jd_upload': 'POST /api/upload_jd',
                'list_jds': 'GET /api/job_descriptions',
                'jd_details': 'GET /api/job_descriptions/<id>',
                'update_jd': 'PUT /api/job_descriptions/<id>',
                'delete_jd': 'DELETE /api/job_descriptions/<id>',
                'jd_health': 'GET /api/jd_health'
            },
            'authentication': {
                'required': True,
                'type': 'JWT Bearer Token',
                'login_endpoint': '/api/login',
                'register_endpoint': '/api/register'
            },
            'timestamp': datetime.utcnow().isoformat()
        })
    
    # Health check endpoint
    @app.route('/health')
    def health_check():
        """Comprehensive health check for US-04"""
        
        # Check database connection
        db_status = 'unknown'
        user_count = 0
        jd_count = 0
        
        try:
            if db:
                # Test database connection
                db.session.execute('SELECT 1')
                db_status = 'connected'
                
                # Get counts
                user_count = User.query.count() if User else 0
                jd_count = JobDescription.query.count()
            else:
                db_status = 'not_configured'
        except Exception as e:
            db_status = f'error: {str(e)}'
        
        return jsonify({
            'success': True,
            'message': 'Application is running',
            'status': {
                'app': 'running',
                'database': db_status,
                'jwt': 'configured',
                'feature': 'US-04: Job Description Upload',
                'user_count': user_count,
                'jd_count': jd_count
            },
            'features': {
                'us01_registration': 'active' if auth_bp else 'not_available',
                'us02_jwt_auth': 'active' if auth_bp else 'not_available',
                'us04_jd_upload': 'active'
            },
            'jd_config': {
                'max_text_length': '50,000 characters',
                'min_text_length': '50 characters',
                'supported_employment_types': ['full-time', 'part-time', 'contract', 'temporary', 'internship'],
                'supported_experience_levels': ['entry-level', 'mid-level', 'senior-level', 'executive', 'not-specified']
            },
            'timestamp': datetime.utcnow().isoformat()
        })
    
    # Global error handlers
    @app.errorhandler(404)
    def not_found(error):
        """Handle 404 errors globally"""
        return jsonify({
            'success': False,
            'message': 'API endpoint not found',
            'error': 'Not Found',
            'available_endpoints': {
                'jd_upload': 'POST /api/upload_jd',
                'list_jds': 'GET /api/job_descriptions',
                'jd_details': 'GET /api/job_descriptions/<id>',
                'health': 'GET /health',
                'home': 'GET /'
            }
        }), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        """Handle 500 errors globally"""
        if db:
            db.session.rollback()
        return jsonify({
            'success': False,
            'message': 'Internal server error',
            'error': 'Internal Server Error'
        }), 500
    
    @app.errorhandler(400)
    def bad_request(error):
        """Handle 400 errors globally"""
        return jsonify({
            'success': False,
            'message': 'Bad request',
            'error': 'Bad Request'
        }), 400
    
    return app

def main():
    """
    Main function to run the Flask application
    """
    print("🚀 Starting US-04: Job Description Upload Application")
    print("=" * 60)
    
    # Create Flask app
    app = create_app()
    
    # Create database tables
    if db:
        with app.app_context():
            try:
                db.create_all()
                print("✅ Database tables created/verified")
            except Exception as e:
                print(f"⚠️  Database setup warning: {e}")
    
    # Print startup information
    print("\n📋 US-04 Features Available:")
    print("• Job Description Upload (POST /api/upload_jd)")
    print("• Job Description Management (GET, PUT, DELETE /api/job_descriptions)")
    print("• JWT Authentication (from US-02)")
    print("• User Management (from US-01)")
    
    print("\n🔗 API Endpoints:")
    print("• Home: GET /")
    print("• Health: GET /health")
    print("• Upload JD: POST /api/upload_jd")
    print("• List JDs: GET /api/job_descriptions")
    print("• JD Details: GET /api/job_descriptions/<id>")
    print("• Update JD: PUT /api/job_descriptions/<id>")
    print("• Delete JD: DELETE /api/job_descriptions/<id>")
    
    print("\n🔐 Authentication:")
    print("• Login: POST /api/login")
    print("• Register: POST /api/register")
    print("• All JD endpoints require JWT authentication")
    
    print("\n🌐 Starting server...")
    print("Access the API at: http://localhost:5000")
    print("API Documentation: http://localhost:5000/")
    
    # Run the application
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True
    )

if __name__ == '__main__':
    main()
