"""
US-04: Job Description Upload - Job Description Model
====================================================

This file contains the SQLAlchemy model for job descriptions.
It handles job description data storage, validation, and relationships.

Tech Stack: SQLAlchemy, PostgreSQL
Dependencies: US-01 (User model and database setup)
"""

import os
import sys
import uuid
from datetime import datetime

# Add paths for imports from previous US
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-01-User-Registration', 'backend'))

try:
    from us01_user_model import db, User
except ImportError as e:
    print(f"⚠️  Import warning: {e}")
    print("Some features may not work without US-01")
    # Create a minimal db object for standalone testing
    from flask_sqlalchemy import SQLAlchemy
    db = SQLAlchemy()

class JobDescription(db.Model):
    """
    Job Description Model for JD Upload and Processing
    
    This model stores job description information including:
    - Unique job description ID (UUID)
    - User ID (foreign key to users table)
    - Job title and company information
    - Full job description text
    - Job details (location, type, experience level)
    - Processing status for future features
    - Timestamps for tracking
    """
    
    __tablename__ = 'job_descriptions'
    
    # Primary key - using UUID for better security
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Foreign key to users table (from US-01)
    user_id = db.Column(db.String(36), db.ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    
    # Job description content
    title = db.Column(db.String(200), nullable=False)
    company_name = db.Column(db.String(100), nullable=True)
    job_description_text = db.Column(db.Text, nullable=False)
    
    # Job details
    location = db.Column(db.String(100), nullable=True)
    employment_type = db.Column(db.String(50), default='full-time', nullable=True)
    experience_level = db.Column(db.String(50), nullable=True)
    salary_range = db.Column(db.String(100), nullable=True)
    
    # Processing status (for future US features)
    is_processed = db.Column(db.Boolean, default=False, nullable=False)
    keywords_extracted = db.Column(db.Boolean, default=False, nullable=False)
    
    # Metadata
    original_source = db.Column(db.String(100), default='manual_input', nullable=True)
    job_url = db.Column(db.Text, nullable=True)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationship to User model (from US-01)
    user = db.relationship('User', backref=db.backref('job_descriptions', lazy=True, cascade='all, delete-orphan'))
    
    def __init__(self, user_id, title, job_description_text, company_name=None, 
                 location=None, employment_type='full-time', experience_level=None,
                 salary_range=None, original_source='manual_input', job_url=None):
        """
        Initialize a new job description
        
        Args:
            user_id (str): ID of the user who uploaded this JD
            title (str): Job title
            job_description_text (str): Full job description content
            company_name (str, optional): Company name
            location (str, optional): Job location
            employment_type (str, optional): Type of employment (default: 'full-time')
            experience_level (str, optional): Required experience level
            salary_range (str, optional): Salary range information
            original_source (str, optional): Source of the JD (default: 'manual_input')
            job_url (str, optional): Original job posting URL
        """
        self.user_id = user_id
        self.title = title.strip()
        self.job_description_text = job_description_text.strip()
        self.company_name = company_name.strip() if company_name else None
        self.location = location.strip() if location else None
        self.employment_type = employment_type
        self.experience_level = experience_level
        self.salary_range = salary_range.strip() if salary_range else None
        self.original_source = original_source
        self.job_url = job_url.strip() if job_url else None
    
    def to_dict(self):
        """
        Convert job description to dictionary for JSON serialization
        
        Returns:
            dict: Job description data as dictionary
        """
        return {
            'id': self.id,
            'user_id': self.user_id,
            'title': self.title,
            'company_name': self.company_name,
            'job_description_text': self.job_description_text,
            'location': self.location,
            'employment_type': self.employment_type,
            'experience_level': self.experience_level,
            'salary_range': self.salary_range,
            'is_processed': self.is_processed,
            'keywords_extracted': self.keywords_extracted,
            'original_source': self.original_source,
            'job_url': self.job_url,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'description_length': len(self.job_description_text) if self.job_description_text else 0
        }
    
    def to_summary_dict(self):
        """
        Convert job description to summary dictionary (without full text)
        
        Returns:
            dict: Job description summary data
        """
        return {
            'id': self.id,
            'title': self.title,
            'company_name': self.company_name,
            'location': self.location,
            'employment_type': self.employment_type,
            'experience_level': self.experience_level,
            'is_processed': self.is_processed,
            'keywords_extracted': self.keywords_extracted,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'description_length': len(self.job_description_text) if self.job_description_text else 0
        }
    
    @classmethod
    def create_job_description(cls, user_id, title, job_description_text, **kwargs):
        """
        Create a new job description with validation
        
        Args:
            user_id (str): ID of the user
            title (str): Job title
            job_description_text (str): Job description content
            **kwargs: Additional job description fields
            
        Returns:
            tuple: (JobDescription object, error_message)
                   Returns (job_description, None) on success
                   Returns (None, error_message) on failure
        """
        try:
            # Validate required fields
            if not title or not title.strip():
                return None, "Job title is required"
            
            if not job_description_text or not job_description_text.strip():
                return None, "Job description text is required"
            
            if len(job_description_text.strip()) < 50:
                return None, "Job description must be at least 50 characters long"
            
            if len(job_description_text.strip()) > 50000:
                return None, "Job description must be less than 50,000 characters"
            
            # Validate user exists
            user = User.query.get(user_id)
            if not user:
                return None, "User not found"
            
            if not user.is_active:
                return None, "User account is not active"
            
            # Create job description
            job_description = cls(
                user_id=user_id,
                title=title,
                job_description_text=job_description_text,
                **kwargs
            )
            
            # Save to database
            db.session.add(job_description)
            db.session.commit()
            
            return job_description, None
            
        except Exception as e:
            db.session.rollback()
            return None, f"Error creating job description: {str(e)}"
    
    @classmethod
    def get_by_user(cls, user_id, limit=None):
        """
        Get job descriptions for a specific user
        
        Args:
            user_id (str): User ID
            limit (int, optional): Maximum number of results
            
        Returns:
            list: List of JobDescription objects
        """
        query = cls.query.filter_by(user_id=user_id).order_by(cls.created_at.desc())
        
        if limit:
            query = query.limit(limit)
            
        return query.all()
    
    @classmethod
    def get_by_id(cls, jd_id, user_id=None):
        """
        Get job description by ID, optionally filtered by user
        
        Args:
            jd_id (str): Job description ID
            user_id (str, optional): User ID for additional security
            
        Returns:
            JobDescription: Job description object or None
        """
        query = cls.query.filter_by(id=jd_id)
        
        if user_id:
            query = query.filter_by(user_id=user_id)
            
        return query.first()
    
    def update_processing_status(self, is_processed=True, keywords_extracted=False):
        """
        Update processing status (for future US features)
        
        Args:
            is_processed (bool): Whether the JD has been processed
            keywords_extracted (bool): Whether keywords have been extracted
        """
        try:
            self.is_processed = is_processed
            self.keywords_extracted = keywords_extracted
            self.updated_at = datetime.utcnow()
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error updating processing status: {e}")
            return False
    
    def delete(self):
        """
        Delete this job description
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            db.session.delete(self)
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error deleting job description: {e}")
            return False
    
    def __repr__(self):
        """String representation of the job description"""
        return f'<JobDescription {self.id}: {self.title} at {self.company_name}>'
