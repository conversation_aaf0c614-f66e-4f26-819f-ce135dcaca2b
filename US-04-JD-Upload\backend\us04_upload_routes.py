"""
US-04: Job Description Upload - Upload Routes
============================================

This file contains the Flask routes for job description upload and management.
It handles JD upload, validation, storage, and retrieval functionality.

Tech Stack: Flask, Flask-JWT-Extended, SQLAlchemy
Dependencies: US-01 (User model), US-02 (JWT authentication)
"""

import os
import sys
from datetime import datetime
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity

# Add paths for imports from previous US
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-01-User-Registration', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-02-Login-JWT-Token', 'backend'))

try:
    from us01_user_model import User, db
    from us04_jd_model import JobDescription
except ImportError as e:
    print(f"⚠️  Import warning: {e}")
    print("Some features may not work without US-01 and US-04 models")

# Create Blueprint for JD upload routes
jd_bp = Blueprint('jd', __name__, url_prefix='/api')

@jd_bp.route('/upload_jd', methods=['POST'])
@jwt_required()
def upload_job_description():
    """
    Upload Job Description Endpoint
    
    POST /api/upload_jd
    
    Accepts job description text and metadata, validates it,
    and saves it to the PostgreSQL database.
    
    Required Headers:
        Authorization: Bearer <access_token>
    
    Request Body (JSON):
        {
            "title": "Job Title",
            "company_name": "Company Name (optional)",
            "job_description_text": "Full job description content",
            "location": "Job Location (optional)",
            "employment_type": "full-time|part-time|contract|temporary|internship (optional)",
            "experience_level": "entry-level|mid-level|senior-level|executive (optional)",
            "salary_range": "Salary information (optional)",
            "job_url": "Original job posting URL (optional)"
        }
    
    Returns:
        JSON response with success/failure status and job description data
    """
    try:
        # Get current user from JWT token
        current_user_id = get_jwt_identity()
        
        # Verify user exists and is active
        user = User.query.get(current_user_id)
        if not user:
            return jsonify({
                'success': False,
                'message': 'User not found'
            }), 404
        
        if not user.is_active:
            return jsonify({
                'success': False,
                'message': 'Account is not active'
            }), 401
        
        # Get request data
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400
        
        # Extract required fields
        title = data.get('title', '').strip()
        job_description_text = data.get('job_description_text', '').strip()
        
        # Extract optional fields
        company_name = data.get('company_name', '').strip() or None
        location = data.get('location', '').strip() or None
        employment_type = data.get('employment_type', 'full-time').strip()
        experience_level = data.get('experience_level', '').strip() or None
        salary_range = data.get('salary_range', '').strip() or None
        job_url = data.get('job_url', '').strip() or None
        
        # Validate required fields
        if not title:
            return jsonify({
                'success': False,
                'message': 'Job title is required'
            }), 400
        
        if not job_description_text:
            return jsonify({
                'success': False,
                'message': 'Job description text is required'
            }), 400
        
        # Validate employment type
        valid_employment_types = ['full-time', 'part-time', 'contract', 'temporary', 'internship']
        if employment_type and employment_type not in valid_employment_types:
            return jsonify({
                'success': False,
                'message': f'Invalid employment type. Must be one of: {", ".join(valid_employment_types)}'
            }), 400
        
        # Validate experience level
        valid_experience_levels = ['entry-level', 'mid-level', 'senior-level', 'executive', 'not-specified']
        if experience_level and experience_level not in valid_experience_levels:
            return jsonify({
                'success': False,
                'message': f'Invalid experience level. Must be one of: {", ".join(valid_experience_levels)}'
            }), 400
        
        # Create job description
        job_description, error = JobDescription.create_job_description(
            user_id=current_user_id,
            title=title,
            job_description_text=job_description_text,
            company_name=company_name,
            location=location,
            employment_type=employment_type,
            experience_level=experience_level,
            salary_range=salary_range,
            original_source='manual_input',
            job_url=job_url
        )
        
        if error:
            return jsonify({
                'success': False,
                'message': error
            }), 400
        
        # Success response
        return jsonify({
            'success': True,
            'message': 'Job description uploaded successfully',
            'job_description': job_description.to_dict()
        }), 201
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error uploading job description: {str(e)}'
        }), 500

@jd_bp.route('/job_descriptions', methods=['GET'])
@jwt_required()
def get_job_descriptions():
    """
    Get Job Descriptions List Endpoint
    
    GET /api/job_descriptions
    
    Returns a list of job descriptions for the current user.
    
    Query Parameters:
        limit (int, optional): Maximum number of results (default: 50)
        summary (bool, optional): Return summary data only (default: false)
    
    Returns:
        JSON response with list of job descriptions
    """
    try:
        # Get current user from JWT token
        current_user_id = get_jwt_identity()
        
        # Get query parameters
        limit = request.args.get('limit', 50, type=int)
        summary_only = request.args.get('summary', 'false').lower() == 'true'
        
        # Validate limit
        if limit > 100:
            limit = 100
        
        # Get job descriptions for user
        job_descriptions = JobDescription.get_by_user(current_user_id, limit=limit)
        
        # Convert to dictionary format
        if summary_only:
            jd_list = [jd.to_summary_dict() for jd in job_descriptions]
        else:
            jd_list = [jd.to_dict() for jd in job_descriptions]
        
        return jsonify({
            'success': True,
            'message': f'Retrieved {len(jd_list)} job descriptions',
            'job_descriptions': jd_list,
            'total_count': len(jd_list)
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error retrieving job descriptions: {str(e)}'
        }), 500

@jd_bp.route('/job_descriptions/<jd_id>', methods=['GET'])
@jwt_required()
def get_job_description(jd_id):
    """
    Get Single Job Description Endpoint
    
    GET /api/job_descriptions/<jd_id>
    
    Returns details of a specific job description.
    
    Returns:
        JSON response with job description details
    """
    try:
        # Get current user from JWT token
        current_user_id = get_jwt_identity()
        
        # Get job description (filtered by user for security)
        job_description = JobDescription.get_by_id(jd_id, user_id=current_user_id)
        
        if not job_description:
            return jsonify({
                'success': False,
                'message': 'Job description not found'
            }), 404
        
        return jsonify({
            'success': True,
            'message': 'Job description retrieved successfully',
            'job_description': job_description.to_dict()
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error retrieving job description: {str(e)}'
        }), 500

@jd_bp.route('/job_descriptions/<jd_id>', methods=['PUT'])
@jwt_required()
def update_job_description(jd_id):
    """
    Update Job Description Endpoint
    
    PUT /api/job_descriptions/<jd_id>
    
    Updates an existing job description.
    
    Request Body (JSON):
        Same as upload_job_description endpoint
    
    Returns:
        JSON response with updated job description data
    """
    try:
        # Get current user from JWT token
        current_user_id = get_jwt_identity()
        
        # Get job description (filtered by user for security)
        job_description = JobDescription.get_by_id(jd_id, user_id=current_user_id)
        
        if not job_description:
            return jsonify({
                'success': False,
                'message': 'Job description not found'
            }), 404
        
        # Get request data
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400
        
        # Update fields if provided
        if 'title' in data:
            title = data['title'].strip()
            if not title:
                return jsonify({
                    'success': False,
                    'message': 'Job title cannot be empty'
                }), 400
            job_description.title = title
        
        if 'job_description_text' in data:
            text = data['job_description_text'].strip()
            if not text:
                return jsonify({
                    'success': False,
                    'message': 'Job description text cannot be empty'
                }), 400
            if len(text) < 50:
                return jsonify({
                    'success': False,
                    'message': 'Job description must be at least 50 characters long'
                }), 400
            job_description.job_description_text = text
        
        if 'company_name' in data:
            job_description.company_name = data['company_name'].strip() or None
        
        if 'location' in data:
            job_description.location = data['location'].strip() or None
        
        if 'employment_type' in data:
            employment_type = data['employment_type'].strip()
            valid_types = ['full-time', 'part-time', 'contract', 'temporary', 'internship']
            if employment_type and employment_type not in valid_types:
                return jsonify({
                    'success': False,
                    'message': f'Invalid employment type. Must be one of: {", ".join(valid_types)}'
                }), 400
            job_description.employment_type = employment_type
        
        if 'experience_level' in data:
            experience_level = data['experience_level'].strip()
            valid_levels = ['entry-level', 'mid-level', 'senior-level', 'executive', 'not-specified']
            if experience_level and experience_level not in valid_levels:
                return jsonify({
                    'success': False,
                    'message': f'Invalid experience level. Must be one of: {", ".join(valid_levels)}'
                }), 400
            job_description.experience_level = experience_level or None
        
        if 'salary_range' in data:
            job_description.salary_range = data['salary_range'].strip() or None
        
        if 'job_url' in data:
            job_description.job_url = data['job_url'].strip() or None
        
        # Update timestamp
        job_description.updated_at = datetime.utcnow()
        
        # Save changes
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Job description updated successfully',
            'job_description': job_description.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'Error updating job description: {str(e)}'
        }), 500

@jd_bp.route('/job_descriptions/<jd_id>', methods=['DELETE'])
@jwt_required()
def delete_job_description(jd_id):
    """
    Delete Job Description Endpoint

    DELETE /api/job_descriptions/<jd_id>

    Deletes a specific job description.

    Returns:
        JSON response confirming deletion
    """
    try:
        # Get current user from JWT token
        current_user_id = get_jwt_identity()

        # Get job description (filtered by user for security)
        job_description = JobDescription.get_by_id(jd_id, user_id=current_user_id)

        if not job_description:
            return jsonify({
                'success': False,
                'message': 'Job description not found'
            }), 404

        # Store info for response
        title = job_description.title
        company = job_description.company_name

        # Delete job description
        if job_description.delete():
            return jsonify({
                'success': True,
                'message': f'Job description "{title}" at {company} deleted successfully'
            }), 200
        else:
            return jsonify({
                'success': False,
                'message': 'Error deleting job description'
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error deleting job description: {str(e)}'
        }), 500

# Health check endpoint for the JD upload service
@jd_bp.route('/jd_health', methods=['GET'])
def jd_health_check():
    """
    Health check endpoint for the JD upload service

    Returns:
        JSON response indicating service status
    """
    return jsonify({
        'success': True,
        'message': 'JD Upload service is running',
        'service': 'US-04 Job Description Upload',
        'timestamp': datetime.utcnow().isoformat()
    }), 200
