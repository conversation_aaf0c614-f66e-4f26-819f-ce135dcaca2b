"""
US-04: Job Description Upload - Database Initialization
======================================================

This script initializes the database for US-04 JD Upload feature.
It creates the job_descriptions table and sets up the necessary relationships.

Tech Stack: Python, SQLAlchemy, PostgreSQL
Dependencies: US-01 (User model and database setup)

Usage:
    python us04_init_db.py

Environment Variables Required:
    - DATABASE_URL or individual DB connection parameters
    - See README_US04.md for complete setup instructions
"""

import os
import sys
from datetime import datetime

# Add paths for imports from previous US
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-01-User-Registration', 'backend'))

try:
    from us01_user_model import db, User
    print("✅ Successfully imported User model from US-01")
except ImportError as e:
    print(f"❌ Error importing from US-01: {e}")
    print("Please ensure US-01 is properly set up before running US-04")
    sys.exit(1)

def create_job_descriptions_table():
    """
    Create the job_descriptions table using SQLAlchemy
    
    This function creates the table structure that matches
    the SQL schema defined in us04_schema.sql
    """
    try:
        # Import the JobDescription model
        from us04_jd_model import JobDescription
        
        # Create all tables (this will create job_descriptions table)
        db.create_all()
        
        print("✅ Job descriptions table created successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error creating job_descriptions table: {e}")
        return False

def verify_database_setup():
    """
    Verify that the database setup is correct
    
    Returns:
        bool: True if setup is valid, False otherwise
    """
    try:
        # Check if users table exists (from US-01)
        users_count = User.query.count()
        print(f"✅ Users table verified - {users_count} users found")
        
        # Check if job_descriptions table exists
        from us04_jd_model import JobDescription
        jd_count = JobDescription.query.count()
        print(f"✅ Job descriptions table verified - {jd_count} job descriptions found")
        
        return True
        
    except Exception as e:
        print(f"❌ Database verification failed: {e}")
        return False

def create_sample_data():
    """
    Create sample job description data for testing
    
    This is optional and only runs if explicitly requested
    """
    try:
        from us04_jd_model import JobDescription
        
        # Check if we have any users to associate with
        sample_user = User.query.first()
        if not sample_user:
            print("⚠️  No users found. Please register a user first using US-01")
            return False
        
        # Check if sample data already exists
        existing_jd = JobDescription.query.filter_by(user_id=sample_user.id).first()
        if existing_jd:
            print("ℹ️  Sample job description data already exists")
            return True
        
        # Create sample job description
        sample_jd = JobDescription(
            user_id=sample_user.id,
            title="Senior Python Developer",
            company_name="TechCorp Solutions",
            job_description_text="""
We are seeking a Senior Python Developer to join our innovative team. 

Key Responsibilities:
- Develop and maintain web applications using Python and Flask
- Design and implement RESTful APIs
- Work with PostgreSQL databases and SQLAlchemy ORM
- Collaborate with frontend developers and UI/UX designers
- Write clean, maintainable, and well-documented code
- Participate in code reviews and technical discussions

Required Skills:
- 5+ years of experience with Python development
- Strong knowledge of Flask framework
- Experience with PostgreSQL and database design
- Familiarity with JWT authentication
- Knowledge of Git version control
- Understanding of software testing principles

Preferred Skills:
- Experience with cloud platforms (AWS, Azure, GCP)
- Knowledge of containerization (Docker)
- Familiarity with CI/CD pipelines
- Experience with frontend technologies (HTML, CSS, JavaScript)

We offer competitive salary, health benefits, flexible working hours, and opportunities for professional growth.
            """.strip(),
            location="San Francisco, CA",
            employment_type="full-time",
            experience_level="senior-level",
            original_source="manual_input"
        )
        
        db.session.add(sample_jd)
        db.session.commit()
        
        print("✅ Sample job description created successfully")
        print(f"   Title: {sample_jd.title}")
        print(f"   Company: {sample_jd.company_name}")
        print(f"   User: {sample_user.email}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating sample data: {e}")
        db.session.rollback()
        return False

def main():
    """
    Main function to initialize the database for US-04
    """
    print("🚀 Initializing US-04: Job Description Upload Database")
    print("=" * 60)
    
    # Step 1: Create the job_descriptions table
    print("\n📋 Step 1: Creating job_descriptions table...")
    if not create_job_descriptions_table():
        print("❌ Failed to create job_descriptions table")
        return False
    
    # Step 2: Verify database setup
    print("\n🔍 Step 2: Verifying database setup...")
    if not verify_database_setup():
        print("❌ Database verification failed")
        return False
    
    # Step 3: Ask about sample data
    print("\n📊 Step 3: Sample data creation...")
    create_sample = input("Do you want to create sample job description data? (y/N): ").lower().strip()
    
    if create_sample in ['y', 'yes']:
        if create_sample_data():
            print("✅ Sample data created successfully")
        else:
            print("⚠️  Sample data creation failed, but database setup is complete")
    else:
        print("ℹ️  Skipping sample data creation")
    
    print("\n🎉 US-04 Database initialization completed successfully!")
    print("\nNext steps:")
    print("1. Start the Flask application: python us04_app.py")
    print("2. Test the JD upload endpoint: POST /api/upload_jd")
    print("3. Check the frontend interface: us04_upload.html")
    
    return True

if __name__ == "__main__":
    # Set up the application context for database operations
    try:
        # Import the Flask app to get the application context
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))
        from us04_app import create_app
        
        app = create_app()
        with app.app_context():
            success = main()
            if not success:
                sys.exit(1)
                
    except ImportError as e:
        print(f"❌ Error importing Flask app: {e}")
        print("Please ensure the backend components are created first")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)
