-- US-04: Job Description Upload - Database Schema
-- ===============================================
-- 
-- This file contains the PostgreSQL database schema for US-04 JD Upload
-- It defines the job_descriptions table structure and relationships
-- 
-- Tech Stack: PostgreSQL 13+
-- Dependencies: US-01 (users table)

-- Note: This assumes the database and UUID extension are already created in US-01
-- If running independently, uncomment the following:
-- CREATE DATABASE dr_resume_db;
-- \c dr_resume_db;
-- CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create job_descriptions table for US-04 JD Upload
CREATE TABLE IF NOT EXISTS job_descriptions (
    -- Primary key using UUID for better security
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    
    -- Foreign key to users table (from US-01)
    user_id VARCHAR(36) NOT NULL,
    
    -- Job description content
    title VARCHAR(200) NOT NULL,
    company_name VARCHAR(100),
    job_description_text TEXT NOT NULL,
    
    -- Job details
    location VARCHAR(100),
    employment_type VARCHAR(50) DEFAULT 'full-time',
    experience_level VARCHAR(50),
    salary_range VARCHAR(100),
    
    -- Processing status
    is_processed BOOLEAN DEFAULT FALSE NOT NULL,
    keywords_extracted BOOLEAN DEFAULT FALSE NOT NULL,
    
    -- Metadata
    original_source VARCHAR(100), -- e.g., 'manual_input', 'job_board', 'company_website'
    job_url TEXT, -- Original job posting URL if available
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    
    -- Constraints
    CONSTRAINT fk_job_descriptions_user_id 
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT chk_employment_type 
        CHECK (employment_type IN ('full-time', 'part-time', 'contract', 'temporary', 'internship')),
    CONSTRAINT chk_experience_level 
        CHECK (experience_level IN ('entry-level', 'mid-level', 'senior-level', 'executive', 'not-specified')),
    CONSTRAINT chk_job_description_length 
        CHECK (char_length(job_description_text) >= 50 AND char_length(job_description_text) <= 50000)
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_job_descriptions_user_id ON job_descriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_job_descriptions_created_at ON job_descriptions(created_at);
CREATE INDEX IF NOT EXISTS idx_job_descriptions_is_processed ON job_descriptions(is_processed);
CREATE INDEX IF NOT EXISTS idx_job_descriptions_title ON job_descriptions(title);
CREATE INDEX IF NOT EXISTS idx_job_descriptions_company ON job_descriptions(company_name);

-- Create trigger to automatically update updated_at timestamp
-- (Reuse the function created in US-01)
CREATE TRIGGER trigger_job_descriptions_updated_at
    BEFORE UPDATE ON job_descriptions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create a view for job description summary (useful for dashboard)
CREATE OR REPLACE VIEW job_descriptions_summary AS
SELECT 
    jd.id,
    jd.user_id,
    jd.title,
    jd.company_name,
    jd.location,
    jd.employment_type,
    jd.experience_level,
    jd.is_processed,
    jd.keywords_extracted,
    jd.created_at,
    u.email as user_email,
    u.first_name,
    u.last_name,
    char_length(jd.job_description_text) as description_length
FROM job_descriptions jd
JOIN users u ON jd.user_id = u.id
WHERE u.is_active = TRUE;

-- Insert sample data for testing (optional)
-- Uncomment the following lines if you want sample data

/*
-- First, get a sample user ID (replace with actual user ID from your users table)
-- INSERT INTO job_descriptions (user_id, title, company_name, job_description_text, location, employment_type, experience_level) 
-- VALUES (
--     (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
--     'Senior Software Engineer',
--     'Tech Corp Inc.',
--     'We are looking for a Senior Software Engineer to join our dynamic team. The ideal candidate will have 5+ years of experience in Python, Flask, PostgreSQL, and modern web development. Responsibilities include designing scalable applications, mentoring junior developers, and collaborating with cross-functional teams. Requirements: Bachelor''s degree in Computer Science, strong problem-solving skills, experience with cloud platforms (AWS/Azure), knowledge of CI/CD pipelines, and excellent communication skills.',
--     'San Francisco, CA',
--     'full-time',
--     'senior-level'
-- );
*/

-- Verify table creation
SELECT 
    table_name, 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'job_descriptions' 
ORDER BY ordinal_position;

-- Display table structure
\d job_descriptions;

-- Show indexes
\di job_descriptions*;

-- Show foreign key constraints
SELECT
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_name='job_descriptions';

-- Show view definition
\d+ job_descriptions_summary;

-- Test query to verify relationships work
-- SELECT COUNT(*) as total_job_descriptions FROM job_descriptions;
-- SELECT COUNT(*) as processed_job_descriptions FROM job_descriptions WHERE is_processed = TRUE;
