# US-04: Job Description Upload - Configuration Guide

## 🔧 Configuration Data to Replace

This document lists all the placeholder data that needs to be replaced with your actual configuration when deploying US-04.

## 📊 Database Configuration

### PostgreSQL Connection String

**File**: `US-04-JD-Upload/backend/us04_app.py`
**Line**: ~60

**Replace this**:
```python
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get(
    'DATABASE_URL',
    'postgresql://username:password@localhost/dr_resume_db'  # ← REPLACE THIS
)
```

**With your actual data**:
```python
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get(
    'DATABASE_URL',
    '************************************************************/your_db_name'
)
```

### Environment Variables (.env file)

**File**: `US-04-JD-Upload/backend/.env` (create this file)

**Replace these values**:
```env
# Database Configuration - REPLACE WITH YOUR ACTUAL VALUES
DATABASE_URL=************************************************************/your_db_name

# JWT Configuration - GENERATE NEW SECRET KEYS
JWT_SECRET_KEY=your-actual-jwt-secret-key-here
SECRET_KEY=your-actual-flask-secret-key-here

# Application Configuration
FLASK_ENV=production  # or development
FLASK_DEBUG=False     # set to True for development
```

## 🔐 Security Keys to Generate

### JWT Secret Key

**Purpose**: Used to sign and verify JWT tokens
**How to generate**:
```python
import secrets
print(secrets.token_urlsafe(32))
# Example output: 'xvz8K9L2mN3pQ4rS5tU6vW7xY8zA1bC2dE3fG4hI5jK6'
```

### Flask Secret Key

**Purpose**: Used for session management and CSRF protection
**How to generate**:
```python
import secrets
print(secrets.token_hex(16))
# Example output: 'a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6'
```

## 🌐 API Configuration

### Base URL Configuration

**File**: `US-04-JD-Upload/frontend/us04_upload.js`
**Line**: ~12

**Replace this**:
```javascript
const API_BASE_URL = 'http://localhost:5000/api';  // ← REPLACE THIS
```

**With your actual API URL**:
```javascript
// For local development
const API_BASE_URL = 'http://localhost:5000/api';

// For production
const API_BASE_URL = 'https://your-domain.com/api';

// For staging
const API_BASE_URL = 'https://staging.your-domain.com/api';
```

### CORS Origins

**File**: `US-04-JD-Upload/backend/us04_app.py`
**Line**: ~85

**Replace this**:
```python
CORS(app, origins=['http://localhost:3000', 'http://127.0.0.1:3000'])  # ← REPLACE THIS
```

**With your actual frontend URLs**:
```python
# For production
CORS(app, origins=['https://your-frontend-domain.com'])

# For development (multiple origins)
CORS(app, origins=[
    'http://localhost:3000',
    'http://127.0.0.1:3000',
    'https://your-staging-domain.com'
])
```

## 📧 Email Configuration (Future Use)

### SMTP Settings

**File**: Create `US-04-JD-Upload/backend/.env`

**Add these for future email features**:
```env
# Email Configuration - REPLACE WITH YOUR ACTUAL SMTP SETTINGS
MAIL_SERVER=smtp.your-email-provider.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_DEFAULT_SENDER=<EMAIL>
```

## 🗄️ Database Schema Customization

### Database Name and User

**File**: `US-04-JD-Upload/database/us04_schema.sql`

**If you want to use different database/user names**, update the comments:
```sql
-- Create database (run this manually in PostgreSQL)
-- CREATE DATABASE your_custom_db_name;  -- ← REPLACE with your DB name
-- \c your_custom_db_name;

-- Create user (if needed)
-- CREATE USER your_custom_user WITH PASSWORD 'your_secure_password';
-- GRANT ALL PRIVILEGES ON DATABASE your_custom_db_name TO your_custom_user;
```

## 🚀 Production Deployment Settings

### Gunicorn Configuration

**File**: Create `US-04-JD-Upload/backend/gunicorn.conf.py`

```python
# Gunicorn configuration - CUSTOMIZE FOR YOUR SERVER
bind = "0.0.0.0:5000"  # ← REPLACE with your host:port
workers = 4  # ← ADJUST based on your server CPU cores
worker_class = "sync"
timeout = 30
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
```

### Nginx Configuration

**File**: Create `/etc/nginx/sites-available/dr-resume-us04`

```nginx
server {
    listen 80;
    server_name your-domain.com;  # ← REPLACE with your domain
    
    location /api/ {
        proxy_pass http://127.0.0.1:5000;  # ← REPLACE with your backend URL
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location / {
        root /path/to/US-04-JD-Upload/frontend;  # ← REPLACE with your path
        try_files $uri $uri/ /us04_upload.html;
    }
}
```

## 🔍 Monitoring Configuration

### Logging Configuration

**File**: `US-04-JD-Upload/backend/us04_app.py`

**Add after app creation**:
```python
import logging
from logging.handlers import RotatingFileHandler

if not app.debug:
    # Configure logging for production
    file_handler = RotatingFileHandler(
        '/path/to/logs/us04.log',  # ← REPLACE with your log path
        maxBytes=10240000,  # 10MB
        backupCount=10
    )
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    app.logger.setLevel(logging.INFO)
    app.logger.info('US-04 JD Upload startup')
```

## 📊 Analytics Configuration (Optional)

### Google Analytics

**File**: `US-04-JD-Upload/frontend/us04_upload.html`

**Add before closing `</head>` tag**:
```html
<!-- Google Analytics - REPLACE WITH YOUR TRACKING ID -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_TRACKING_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_TRACKING_ID');  // ← REPLACE with your tracking ID
</script>
```

## 🔒 SSL/TLS Configuration

### SSL Certificate Paths

**File**: Nginx configuration

```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;  # ← REPLACE with your domain
    
    ssl_certificate /path/to/your/certificate.crt;      # ← REPLACE with your cert path
    ssl_certificate_key /path/to/your/private.key;      # ← REPLACE with your key path
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    
    # Rest of configuration...
}
```

## ✅ Configuration Checklist

Before deploying US-04, ensure you have:

- [ ] Updated database connection string
- [ ] Generated new JWT and Flask secret keys
- [ ] Updated API base URL in frontend
- [ ] Configured CORS origins
- [ ] Set up environment variables (.env file)
- [ ] Configured web server (Nginx/Apache)
- [ ] Set up SSL certificates (for production)
- [ ] Configured logging paths
- [ ] Updated any hardcoded paths or URLs
- [ ] Tested all configurations in staging environment

## 🆘 Quick Configuration Test

Run this script to verify your configuration:

```bash
# Test database connection
python -c "
import os
import psycopg2
try:
    conn = psycopg2.connect(os.environ.get('DATABASE_URL'))
    print('✅ Database connection successful')
    conn.close()
except Exception as e:
    print(f'❌ Database connection failed: {e}')
"

# Test JWT secret
python -c "
import os
jwt_secret = os.environ.get('JWT_SECRET_KEY')
if jwt_secret and len(jwt_secret) >= 32:
    print('✅ JWT secret key is configured')
else:
    print('❌ JWT secret key is missing or too short')
"
```

---

**Important**: Never commit actual passwords, API keys, or secret keys to version control. Always use environment variables or secure configuration management systems.
