<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dr. Resume - Upload Job Description (US-04)</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            color: white !important;
        }
        
        .nav-link {
            color: rgba(255,255,255,0.9) !important;
        }
        
        .nav-link:hover {
            color: white !important;
        }
        
        .upload-container {
            padding: 30px 0;
        }
        
        .upload-card {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }
        
        /* Textarea input for JD (as per requirements) */
        .jd-textarea {
            min-height: 300px;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 20px;
            font-size: 14px;
            line-height: 1.6;
            transition: all 0.3s ease;
            resize: vertical;
        }
        
        .jd-textarea:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .jd-textarea.is-valid {
            border-color: #28a745;
        }
        
        .jd-textarea.is-invalid {
            border-color: #dc3545;
        }
        
        /* Submit button (as per requirements) */
        .submit-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .submit-btn:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: none;
        }
        
        /* Loading spinner (as per requirements) */
        .loading-spinner {
            display: none;
        }
        
        .loading-spinner.show {
            display: inline-block;
        }
        
        /* Success/Error messages (as per requirements) */
        .alert {
            border-radius: 10px;
            border: none;
            padding: 15px 20px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
        }
        
        .alert-danger {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
        }
        
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }
        
        .form-control {
            border-radius: 8px;
            border: 1px solid #dee2e6;
            padding: 10px 15px;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .character-count {
            font-size: 12px;
            color: #6c757d;
            text-align: right;
            margin-top: 5px;
        }
        
        .character-count.warning {
            color: #ffc107;
        }
        
        .character-count.danger {
            color: #dc3545;
        }
        
        .feature-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 20px;
        }
        
        .help-text {
            font-size: 13px;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .required {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-file-alt me-2"></i>
                Dr. Resume
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="goToRegistration()">
                            <i class="fas fa-user-plus me-1"></i>Register
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="goToLogin()">
                            <i class="fas fa-sign-in-alt me-1"></i>Login
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="goToResumeUpload()">
                            <i class="fas fa-upload me-1"></i>Upload Resume
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="#">
                            <i class="fas fa-briefcase me-1"></i>Upload Job Description
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="logout()">
                            <i class="fas fa-sign-out-alt me-1"></i>Logout
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container upload-container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="upload-card">
                    <!-- Feature Badge -->
                    <div class="text-center">
                        <span class="feature-badge">US-04: Job Description Upload</span>
                    </div>
                    
                    <!-- Header -->
                    <div class="text-center mb-4">
                        <h2 class="mb-3">
                            <i class="fas fa-briefcase text-primary me-2"></i>
                            Upload Job Description
                        </h2>
                        <p class="text-muted">
                            Paste the job description text to analyze and match with your resume
                        </p>
                    </div>
                    
                    <!-- Alert Messages (Success/Error as per requirements) -->
                    <div id="alertContainer"></div>
                    
                    <!-- Upload Form -->
                    <form id="jdUploadForm">
                        <!-- Job Title -->
                        <div class="mb-3">
                            <label for="jobTitle" class="form-label">
                                Job Title <span class="required">*</span>
                            </label>
                            <input type="text" class="form-control" id="jobTitle" name="title" 
                                   placeholder="e.g., Senior Software Engineer" required>
                            <div class="help-text">Enter the job title from the posting</div>
                        </div>
                        
                        <!-- Company Name -->
                        <div class="mb-3">
                            <label for="companyName" class="form-label">Company Name</label>
                            <input type="text" class="form-control" id="companyName" name="company_name" 
                                   placeholder="e.g., TechCorp Solutions">
                            <div class="help-text">Optional: Company name for better organization</div>
                        </div>
                        
                        <!-- Job Description Text (Textarea as per requirements) -->
                        <div class="mb-3">
                            <label for="jobDescription" class="form-label">
                                Job Description <span class="required">*</span>
                            </label>
                            <textarea class="form-control jd-textarea" id="jobDescription" 
                                      name="job_description_text" 
                                      placeholder="Paste the complete job description here...

Example:
We are looking for a Senior Software Engineer to join our team...

Responsibilities:
- Develop and maintain web applications
- Collaborate with cross-functional teams
- Write clean, maintainable code

Requirements:
- 5+ years of experience in Python
- Knowledge of Flask, PostgreSQL
- Strong problem-solving skills

Benefits:
- Competitive salary
- Health insurance
- Flexible working hours" 
                                      required></textarea>
                            <div class="character-count" id="characterCount">0 / 50,000 characters</div>
                            <div class="help-text">
                                Minimum 50 characters, maximum 50,000 characters. 
                                Include job responsibilities, requirements, and benefits for better analysis.
                            </div>
                        </div>
                        
                        <!-- Additional Details Row -->
                        <div class="row">
                            <!-- Location -->
                            <div class="col-md-6 mb-3">
                                <label for="location" class="form-label">Location</label>
                                <input type="text" class="form-control" id="location" name="location" 
                                       placeholder="e.g., San Francisco, CA">
                            </div>
                            
                            <!-- Employment Type -->
                            <div class="col-md-6 mb-3">
                                <label for="employmentType" class="form-label">Employment Type</label>
                                <select class="form-control" id="employmentType" name="employment_type">
                                    <option value="full-time">Full-time</option>
                                    <option value="part-time">Part-time</option>
                                    <option value="contract">Contract</option>
                                    <option value="temporary">Temporary</option>
                                    <option value="internship">Internship</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Experience Level and Salary -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="experienceLevel" class="form-label">Experience Level</label>
                                <select class="form-control" id="experienceLevel" name="experience_level">
                                    <option value="">Select level</option>
                                    <option value="entry-level">Entry Level</option>
                                    <option value="mid-level">Mid Level</option>
                                    <option value="senior-level">Senior Level</option>
                                    <option value="executive">Executive</option>
                                    <option value="not-specified">Not Specified</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="salaryRange" class="form-label">Salary Range</label>
                                <input type="text" class="form-control" id="salaryRange" name="salary_range" 
                                       placeholder="e.g., $80,000 - $120,000">
                            </div>
                        </div>
                        
                        <!-- Job URL -->
                        <div class="mb-4">
                            <label for="jobUrl" class="form-label">Job Posting URL</label>
                            <input type="url" class="form-control" id="jobUrl" name="job_url" 
                                   placeholder="https://company.com/careers/job-posting">
                            <div class="help-text">Optional: Link to the original job posting</div>
                        </div>
                        
                        <!-- Submit Button (as per requirements) -->
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary submit-btn" id="submitBtn">
                                <span class="loading-spinner spinner-border spinner-border-sm me-2" id="loadingSpinner"></span>
                                <i class="fas fa-upload me-2"></i>
                                Upload Job Description
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="us04_upload.js"></script>
</body>
</html>
