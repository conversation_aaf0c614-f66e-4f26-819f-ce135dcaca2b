/**
 * US-04: Job Description Upload - Frontend JavaScript
 * ==================================================
 * 
 * This file handles the frontend logic for job description upload.
 * It manages form submission, validation, API calls, and user feedback.
 * 
 * Tech Stack: Vanilla JavaScript, Bootstrap, Fetch API
 * Dependencies: US-02 (JWT authentication)
 */

// Configuration
const API_BASE_URL = 'http://localhost:5000/api';
const MAX_DESCRIPTION_LENGTH = 50000;
const MIN_DESCRIPTION_LENGTH = 50;

// DOM Elements
let jdUploadForm;
let submitBtn;
let loadingSpinner;
let alertContainer;
let jobDescriptionTextarea;
let characterCount;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeElements();
    setupEventListeners();
    checkAuthentication();
});

/**
 * Initialize DOM elements
 */
function initializeElements() {
    jdUploadForm = document.getElementById('jdUploadForm');
    submitBtn = document.getElementById('submitBtn');
    loadingSpinner = document.getElementById('loadingSpinner');
    alertContainer = document.getElementById('alertContainer');
    jobDescriptionTextarea = document.getElementById('jobDescription');
    characterCount = document.getElementById('characterCount');
}

/**
 * Setup event listeners
 */
function setupEventListeners() {
    // Form submission
    if (jdUploadForm) {
        jdUploadForm.addEventListener('submit', handleFormSubmit);
    }
    
    // Character count for job description textarea
    if (jobDescriptionTextarea) {
        jobDescriptionTextarea.addEventListener('input', updateCharacterCount);
        jobDescriptionTextarea.addEventListener('paste', function() {
            // Update character count after paste
            setTimeout(updateCharacterCount, 10);
        });
    }
    
    // Real-time validation
    const inputs = document.querySelectorAll('input[required], textarea[required]');
    inputs.forEach(input => {
        input.addEventListener('blur', validateField);
        input.addEventListener('input', clearFieldError);
    });
}

/**
 * Check if user is authenticated
 */
function checkAuthentication() {
    const token = localStorage.getItem('access_token');
    
    if (!token) {
        showAlert('warning', 'Please log in to upload job descriptions.', true);
        setTimeout(() => {
            goToLogin();
        }, 3000);
        return false;
    }
    
    return true;
}

/**
 * Handle form submission
 */
async function handleFormSubmit(event) {
    event.preventDefault();
    
    if (!checkAuthentication()) {
        return;
    }
    
    // Validate form
    if (!validateForm()) {
        return;
    }
    
    // Show loading state
    setLoadingState(true);
    
    try {
        // Prepare form data
        const formData = new FormData(jdUploadForm);
        const jobDescriptionData = {
            title: formData.get('title').trim(),
            company_name: formData.get('company_name').trim() || null,
            job_description_text: formData.get('job_description_text').trim(),
            location: formData.get('location').trim() || null,
            employment_type: formData.get('employment_type'),
            experience_level: formData.get('experience_level') || null,
            salary_range: formData.get('salary_range').trim() || null,
            job_url: formData.get('job_url').trim() || null
        };
        
        // Make API call
        const response = await fetch(`${API_BASE_URL}/upload_jd`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            },
            body: JSON.stringify(jobDescriptionData)
        });
        
        const result = await response.json();
        
        if (response.ok && result.success) {
            // Success
            showAlert('success', 
                `Job description "${result.job_description.title}" uploaded successfully! ` +
                `You can now proceed to match it with your resume.`
            );
            
            // Reset form
            jdUploadForm.reset();
            updateCharacterCount();
            
            // Optional: Redirect to dashboard after delay
            setTimeout(() => {
                // You can redirect to dashboard or resume upload page
                console.log('Job description uploaded:', result.job_description);
            }, 2000);
            
        } else {
            // Error from API
            showAlert('danger', result.message || 'Failed to upload job description');
        }
        
    } catch (error) {
        console.error('Upload error:', error);
        
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            showAlert('danger', 'Unable to connect to server. Please check your connection and try again.');
        } else {
            showAlert('danger', 'An unexpected error occurred. Please try again.');
        }
    } finally {
        setLoadingState(false);
    }
}

/**
 * Validate the entire form
 */
function validateForm() {
    let isValid = true;
    
    // Clear previous alerts
    clearAlerts();
    
    // Validate job title
    const title = document.getElementById('jobTitle').value.trim();
    if (!title) {
        showFieldError('jobTitle', 'Job title is required');
        isValid = false;
    }
    
    // Validate job description text
    const description = jobDescriptionTextarea.value.trim();
    if (!description) {
        showFieldError('jobDescription', 'Job description text is required');
        isValid = false;
    } else if (description.length < MIN_DESCRIPTION_LENGTH) {
        showFieldError('jobDescription', `Job description must be at least ${MIN_DESCRIPTION_LENGTH} characters long`);
        isValid = false;
    } else if (description.length > MAX_DESCRIPTION_LENGTH) {
        showFieldError('jobDescription', `Job description must be less than ${MAX_DESCRIPTION_LENGTH} characters`);
        isValid = false;
    }
    
    // Validate URL if provided
    const jobUrl = document.getElementById('jobUrl').value.trim();
    if (jobUrl && !isValidUrl(jobUrl)) {
        showFieldError('jobUrl', 'Please enter a valid URL');
        isValid = false;
    }
    
    return isValid;
}

/**
 * Validate individual field
 */
function validateField(event) {
    const field = event.target;
    const value = field.value.trim();
    
    clearFieldError(field.id);
    
    if (field.hasAttribute('required') && !value) {
        showFieldError(field.id, `${field.labels[0].textContent.replace('*', '').trim()} is required`);
        return false;
    }
    
    if (field.id === 'jobDescription') {
        if (value && value.length < MIN_DESCRIPTION_LENGTH) {
            showFieldError(field.id, `Must be at least ${MIN_DESCRIPTION_LENGTH} characters long`);
            return false;
        }
    }
    
    if (field.type === 'url' && value && !isValidUrl(value)) {
        showFieldError(field.id, 'Please enter a valid URL');
        return false;
    }
    
    return true;
}

/**
 * Update character count for job description
 */
function updateCharacterCount() {
    const text = jobDescriptionTextarea.value;
    const length = text.length;
    
    characterCount.textContent = `${length.toLocaleString()} / ${MAX_DESCRIPTION_LENGTH.toLocaleString()} characters`;
    
    // Update styling based on length
    characterCount.className = 'character-count';
    
    if (length > MAX_DESCRIPTION_LENGTH * 0.9) {
        characterCount.classList.add('danger');
    } else if (length > MAX_DESCRIPTION_LENGTH * 0.8) {
        characterCount.classList.add('warning');
    }
    
    // Update textarea styling
    jobDescriptionTextarea.classList.remove('is-valid', 'is-invalid');
    
    if (length >= MIN_DESCRIPTION_LENGTH && length <= MAX_DESCRIPTION_LENGTH) {
        jobDescriptionTextarea.classList.add('is-valid');
    } else if (length > 0) {
        jobDescriptionTextarea.classList.add('is-invalid');
    }
}

/**
 * Show field-specific error
 */
function showFieldError(fieldId, message) {
    const field = document.getElementById(fieldId);
    field.classList.add('is-invalid');
    
    // Remove existing error message
    const existingError = field.parentNode.querySelector('.invalid-feedback');
    if (existingError) {
        existingError.remove();
    }
    
    // Add new error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'invalid-feedback';
    errorDiv.textContent = message;
    field.parentNode.appendChild(errorDiv);
}

/**
 * Clear field error
 */
function clearFieldError(fieldId) {
    const field = typeof fieldId === 'string' ? document.getElementById(fieldId) : fieldId;
    field.classList.remove('is-invalid');
    
    const errorDiv = field.parentNode.querySelector('.invalid-feedback');
    if (errorDiv) {
        errorDiv.remove();
    }
}

/**
 * Set loading state
 */
function setLoadingState(isLoading) {
    if (isLoading) {
        submitBtn.disabled = true;
        loadingSpinner.classList.add('show');
        submitBtn.innerHTML = `
            <span class="loading-spinner spinner-border spinner-border-sm me-2 show"></span>
            <i class="fas fa-upload me-2"></i>
            Uploading...
        `;
    } else {
        submitBtn.disabled = false;
        loadingSpinner.classList.remove('show');
        submitBtn.innerHTML = `
            <span class="loading-spinner spinner-border spinner-border-sm me-2"></span>
            <i class="fas fa-upload me-2"></i>
            Upload Job Description
        `;
    }
}

/**
 * Show alert message
 */
function showAlert(type, message, persistent = false) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.appendChild(alertDiv);
    
    // Auto-dismiss after 5 seconds unless persistent
    if (!persistent) {
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}

/**
 * Clear all alerts
 */
function clearAlerts() {
    alertContainer.innerHTML = '';
}

/**
 * Validate URL format
 */
function isValidUrl(string) {
    try {
        new URL(string);
        return true;
    } catch (_) {
        return false;
    }
}

// Navigation functions (to integrate with other US features)
function goToRegistration() {
    window.location.href = '../../US-01-User-Registration/frontend/us01_register.html';
}

function goToLogin() {
    window.location.href = '../../US-02-Login-JWT-Token/frontend/us02_login.html';
}

function goToResumeUpload() {
    window.location.href = '../../US-03-Resume-Upload/frontend/us03_upload.html';
}

function logout() {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    showAlert('info', 'Logged out successfully');
    setTimeout(() => {
        goToLogin();
    }, 1000);
}
