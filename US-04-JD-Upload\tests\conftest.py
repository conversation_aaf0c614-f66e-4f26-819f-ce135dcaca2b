"""
US-04: Job Description Upload - Test Configuration
=================================================

This file contains pytest configuration and shared fixtures for US-04 tests.
It sets up the test environment and provides common test utilities.

Tech Stack: pytest, Flask-Testing
"""

import pytest
import sys
import os
from datetime import datetime

# Add backend directories to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-01-User-Registration', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-02-Login-JWT-Token', 'backend'))

# Test configuration
pytest_plugins = []

def pytest_configure(config):
    """Configure pytest settings"""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "unit: marks tests as unit tests"
    )

@pytest.fixture(scope="session")
def test_config():
    """Test configuration settings"""
    return {
        'TESTING': True,
        'SQLALCHEMY_DATABASE_URI': 'sqlite:///:memory:',
        'JWT_SECRET_KEY': 'test-secret-key-for-us04',
        'SECRET_KEY': 'test-secret-key',
        'WTF_CSRF_ENABLED': False,
        'SQLALCHEMY_TRACK_MODIFICATIONS': False
    }

@pytest.fixture
def sample_job_description_data():
    """Sample job description data for testing"""
    return {
        'title': 'Senior Python Developer',
        'company_name': 'TechCorp Solutions',
        'job_description_text': """
We are seeking a Senior Python Developer to join our innovative team.

Key Responsibilities:
- Develop and maintain web applications using Python and Flask
- Design and implement RESTful APIs
- Work with PostgreSQL databases and SQLAlchemy ORM
- Collaborate with frontend developers and UI/UX designers
- Write clean, maintainable, and well-documented code
- Participate in code reviews and technical discussions

Required Skills:
- 5+ years of experience with Python development
- Strong knowledge of Flask framework
- Experience with PostgreSQL and database design
- Familiarity with JWT authentication
- Knowledge of Git version control
- Understanding of software testing principles

Preferred Skills:
- Experience with cloud platforms (AWS, Azure, GCP)
- Knowledge of containerization (Docker)
- Familiarity with CI/CD pipelines
- Experience with frontend technologies (HTML, CSS, JavaScript)

We offer competitive salary, health benefits, flexible working hours, and opportunities for professional growth.
        """.strip(),
        'location': 'San Francisco, CA',
        'employment_type': 'full-time',
        'experience_level': 'senior-level',
        'salary_range': '$120,000 - $150,000',
        'job_url': 'https://techcorp.com/careers/senior-python-developer'
    }

@pytest.fixture
def minimal_job_description_data():
    """Minimal job description data for testing"""
    return {
        'title': 'Software Developer',
        'job_description_text': 'Looking for a software developer with programming experience. Must have good problem-solving skills and ability to work in a team environment.'
    }

@pytest.fixture
def invalid_job_description_data():
    """Invalid job description data for testing validation"""
    return [
        # Missing title
        {
            'job_description_text': 'Valid description text here...' * 10
        },
        # Missing description
        {
            'title': 'Valid Title'
        },
        # Description too short
        {
            'title': 'Valid Title',
            'job_description_text': 'Short'
        },
        # Invalid employment type
        {
            'title': 'Valid Title',
            'job_description_text': 'Valid description text here...' * 10,
            'employment_type': 'invalid-type'
        },
        # Invalid experience level
        {
            'title': 'Valid Title',
            'job_description_text': 'Valid description text here...' * 10,
            'experience_level': 'invalid-level'
        }
    ]

@pytest.fixture
def sample_user_data():
    """Sample user data for testing"""
    return {
        'email': '<EMAIL>',
        'password': 'password123',
        'first_name': 'Test',
        'last_name': 'User'
    }

# Test utilities
class TestUtils:
    """Utility functions for testing"""
    
    @staticmethod
    def create_test_user(app, db, User, email='<EMAIL>'):
        """Create a test user"""
        with app.app_context():
            user = User(
                email=email,
                password='password123',
                first_name='Test',
                last_name='User'
            )
            db.session.add(user)
            db.session.commit()
            return user
    
    @staticmethod
    def authenticate_user(client, email='<EMAIL>', password='password123'):
        """Authenticate a user and return access token"""
        login_data = {
            'email': email,
            'password': password
        }
        
        response = client.post('/api/login',
                             data=json.dumps(login_data),
                             content_type='application/json')
        
        if response.status_code == 200:
            data = json.loads(response.data)
            return data['tokens']['access_token']
        return None
    
    @staticmethod
    def create_auth_headers(token):
        """Create authorization headers"""
        return {'Authorization': f'Bearer {token}'}
    
    @staticmethod
    def assert_job_description_structure(jd_dict):
        """Assert that job description dictionary has correct structure"""
        required_fields = [
            'id', 'user_id', 'title', 'job_description_text',
            'created_at', 'updated_at', 'is_processed', 'keywords_extracted'
        ]
        
        for field in required_fields:
            assert field in jd_dict, f"Missing required field: {field}"
        
        # Check data types
        assert isinstance(jd_dict['is_processed'], bool)
        assert isinstance(jd_dict['keywords_extracted'], bool)
        assert isinstance(jd_dict['description_length'], int)

# Make TestUtils available as a fixture
@pytest.fixture
def test_utils():
    """Test utilities fixture"""
    return TestUtils
