"""
US-04: Job Description Upload - Test Suite
==========================================

This file contains comprehensive tests for US-04 JD Upload functionality.
It tests the job description model, upload routes, and API endpoints.

Tech Stack: pytest, Flask-Testing, SQLAlchemy
Dependencies: US-01 (User model), US-02 (JWT authentication)
"""

import pytest
import json
import sys
import os
from datetime import datetime

# Add backend directories to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-01-User-Registration', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-02-Login-JWT-Token', 'backend'))

from us04_app import create_app
from us01_user_model import db, User
from us04_jd_model import JobDescription

class TestJobDescriptionModel:
    """Test cases for JobDescription model functionality"""
    
    @pytest.fixture
    def app(self):
        """Create test Flask application"""
        app = create_app()
        app.config['TESTING'] = True
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        app.config['JWT_SECRET_KEY'] = 'test-secret-key'
        
        with app.app_context():
            db.create_all()
            yield app
            db.drop_all()
    
    @pytest.fixture
    def client(self, app):
        """Create test client"""
        return app.test_client()
    
    @pytest.fixture
    def sample_user(self, app):
        """Create a sample user for testing"""
        with app.app_context():
            user = User(
                email='<EMAIL>',
                password='password123',
                first_name='Test',
                last_name='User'
            )
            db.session.add(user)
            db.session.commit()
            return user
    
    def test_job_description_creation(self, app, sample_user):
        """Test job description creation with valid data"""
        with app.app_context():
            jd = JobDescription(
                user_id=sample_user.id,
                title='Senior Python Developer',
                job_description_text='We are looking for a Senior Python Developer with 5+ years of experience...',
                company_name='TechCorp',
                location='San Francisco, CA',
                employment_type='full-time',
                experience_level='senior-level'
            )
            
            db.session.add(jd)
            db.session.commit()
            
            # Verify creation
            assert jd.id is not None
            assert jd.title == 'Senior Python Developer'
            assert jd.user_id == sample_user.id
            assert jd.company_name == 'TechCorp'
            assert jd.employment_type == 'full-time'
            assert jd.is_processed == False
            assert jd.keywords_extracted == False
    
    def test_job_description_to_dict(self, app, sample_user):
        """Test job description to_dict method"""
        with app.app_context():
            jd = JobDescription(
                user_id=sample_user.id,
                title='Software Engineer',
                job_description_text='Looking for a software engineer...',
                company_name='StartupCorp'
            )
            
            db.session.add(jd)
            db.session.commit()
            
            jd_dict = jd.to_dict()
            
            assert jd_dict['id'] == jd.id
            assert jd_dict['title'] == 'Software Engineer'
            assert jd_dict['company_name'] == 'StartupCorp'
            assert jd_dict['user_id'] == sample_user.id
            assert 'created_at' in jd_dict
            assert 'description_length' in jd_dict
    
    def test_job_description_create_method(self, app, sample_user):
        """Test JobDescription.create_job_description class method"""
        with app.app_context():
            jd, error = JobDescription.create_job_description(
                user_id=sample_user.id,
                title='Data Scientist',
                job_description_text='We need a data scientist with machine learning experience. ' * 10,
                company_name='DataCorp',
                location='New York, NY'
            )
            
            assert error is None
            assert jd is not None
            assert jd.title == 'Data Scientist'
            assert jd.company_name == 'DataCorp'
            assert jd.location == 'New York, NY'
    
    def test_job_description_validation_errors(self, app, sample_user):
        """Test job description validation with invalid data"""
        with app.app_context():
            # Test empty title
            jd, error = JobDescription.create_job_description(
                user_id=sample_user.id,
                title='',
                job_description_text='Valid description text here...'
            )
            assert jd is None
            assert 'title is required' in error
            
            # Test empty description
            jd, error = JobDescription.create_job_description(
                user_id=sample_user.id,
                title='Valid Title',
                job_description_text=''
            )
            assert jd is None
            assert 'description text is required' in error
            
            # Test description too short
            jd, error = JobDescription.create_job_description(
                user_id=sample_user.id,
                title='Valid Title',
                job_description_text='Short'
            )
            assert jd is None
            assert 'at least 50 characters' in error
    
    def test_get_by_user(self, app, sample_user):
        """Test getting job descriptions by user"""
        with app.app_context():
            # Create multiple job descriptions
            jd1 = JobDescription(
                user_id=sample_user.id,
                title='Job 1',
                job_description_text='Description for job 1...' * 10
            )
            jd2 = JobDescription(
                user_id=sample_user.id,
                title='Job 2',
                job_description_text='Description for job 2...' * 10
            )
            
            db.session.add_all([jd1, jd2])
            db.session.commit()
            
            # Test retrieval
            user_jds = JobDescription.get_by_user(sample_user.id)
            assert len(user_jds) == 2
            
            # Test with limit
            limited_jds = JobDescription.get_by_user(sample_user.id, limit=1)
            assert len(limited_jds) == 1

class TestJobDescriptionAPI:
    """Test cases for Job Description API endpoints"""
    
    @pytest.fixture
    def app(self):
        """Create test Flask application"""
        app = create_app()
        app.config['TESTING'] = True
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        app.config['JWT_SECRET_KEY'] = 'test-secret-key'
        
        with app.app_context():
            db.create_all()
            yield app
            db.drop_all()
    
    @pytest.fixture
    def client(self, app):
        """Create test client"""
        return app.test_client()
    
    @pytest.fixture
    def auth_user(self, app, client):
        """Create and authenticate a user"""
        with app.app_context():
            # Register user
            user_data = {
                'email': '<EMAIL>',
                'password': 'password123',
                'first_name': 'Test',
                'last_name': 'User'
            }
            
            response = client.post('/api/register', 
                                 data=json.dumps(user_data),
                                 content_type='application/json')
            
            # Login user
            login_data = {
                'email': '<EMAIL>',
                'password': 'password123'
            }
            
            response = client.post('/api/login',
                                 data=json.dumps(login_data),
                                 content_type='application/json')
            
            data = json.loads(response.data)
            return data['tokens']['access_token']
    
    def test_upload_jd_success(self, client, auth_user):
        """Test successful job description upload"""
        jd_data = {
            'title': 'Senior Software Engineer',
            'company_name': 'TechCorp Inc.',
            'job_description_text': 'We are looking for a Senior Software Engineer to join our team. ' * 10,
            'location': 'San Francisco, CA',
            'employment_type': 'full-time',
            'experience_level': 'senior-level',
            'salary_range': '$120,000 - $150,000'
        }
        
        response = client.post('/api/upload_jd',
                             data=json.dumps(jd_data),
                             content_type='application/json',
                             headers={'Authorization': f'Bearer {auth_user}'})
        
        assert response.status_code == 201
        data = json.loads(response.data)
        assert data['success'] == True
        assert 'job_description' in data
        assert data['job_description']['title'] == 'Senior Software Engineer'
    
    def test_upload_jd_validation_errors(self, client, auth_user):
        """Test job description upload with validation errors"""
        # Test missing title
        jd_data = {
            'job_description_text': 'Valid description text here...' * 10
        }
        
        response = client.post('/api/upload_jd',
                             data=json.dumps(jd_data),
                             content_type='application/json',
                             headers={'Authorization': f'Bearer {auth_user}'})
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['success'] == False
        assert 'title is required' in data['message']
    
    def test_upload_jd_unauthorized(self, client):
        """Test job description upload without authentication"""
        jd_data = {
            'title': 'Test Job',
            'job_description_text': 'Test description...' * 10
        }
        
        response = client.post('/api/upload_jd',
                             data=json.dumps(jd_data),
                             content_type='application/json')
        
        assert response.status_code == 401
    
    def test_get_job_descriptions(self, client, auth_user):
        """Test getting job descriptions list"""
        # First upload a job description
        jd_data = {
            'title': 'Test Job',
            'job_description_text': 'Test description for the job posting...' * 10
        }
        
        client.post('/api/upload_jd',
                   data=json.dumps(jd_data),
                   content_type='application/json',
                   headers={'Authorization': f'Bearer {auth_user}'})
        
        # Get job descriptions
        response = client.get('/api/job_descriptions',
                            headers={'Authorization': f'Bearer {auth_user}'})
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] == True
        assert len(data['job_descriptions']) == 1
        assert data['job_descriptions'][0]['title'] == 'Test Job'
    
    def test_get_single_job_description(self, client, auth_user):
        """Test getting a single job description"""
        # Upload a job description
        jd_data = {
            'title': 'Specific Job',
            'job_description_text': 'Specific job description content...' * 10
        }
        
        upload_response = client.post('/api/upload_jd',
                                    data=json.dumps(jd_data),
                                    content_type='application/json',
                                    headers={'Authorization': f'Bearer {auth_user}'})
        
        upload_data = json.loads(upload_response.data)
        jd_id = upload_data['job_description']['id']
        
        # Get specific job description
        response = client.get(f'/api/job_descriptions/{jd_id}',
                            headers={'Authorization': f'Bearer {auth_user}'})
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] == True
        assert data['job_description']['title'] == 'Specific Job'
    
    def test_health_check(self, client):
        """Test JD health check endpoint"""
        response = client.get('/api/jd_health')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] == True
        assert 'US-04' in data['service']

if __name__ == '__main__':
    pytest.main([__file__])
