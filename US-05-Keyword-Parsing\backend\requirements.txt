# US-05: Keyword Parsing - Python Dependencies
# ============================================
# 
# This file contains all Python packages required for US-05 Keyword Parsing feature
# Install with: pip install -r requirements.txt
# 
# Dependencies include packages from US-01 through US-04, plus new NLP requirements

# Core Flask Framework (from previous US)
Flask==2.3.3
Werkzeug==2.3.7

# Database ORM and PostgreSQL (from previous US)
Flask-SQLAlchemy==3.0.5
SQLAlchemy==2.0.21
psycopg2-binary==2.9.7

# JWT Authentication (from US-02)
Flask-JWT-Extended==4.5.3
PyJWT==2.8.0

# Password Hashing (from US-01)
bcrypt==4.0.1

# CORS Support for Frontend Integration (from previous US)
Flask-CORS==4.0.0

# Environment Variables Management (from previous US)
python-dotenv==1.0.0

# Date and Time Utilities (from previous US)
python-dateutil==2.8.2

# JSON Handling and Validation (from previous US)
jsonschema==4.19.1

# File Processing (from US-03)
PyPDF2==3.0.1
python-docx==0.8.11

# NEW: Natural Language Processing Libraries for US-05
# ====================================================

# spaCy - Advanced NLP library
spacy==3.6.1

# NLTK - Natural Language Toolkit
nltk==3.8.1

# Additional NLP utilities
textblob==0.17.1

# Text preprocessing and cleaning
regex==2023.8.8

# Language detection (optional)
langdetect==1.0.9

# Fuzzy string matching for keyword normalization
fuzzywuzzy==0.18.0
python-Levenshtein==0.21.1

# Scientific computing for NLP
numpy==1.24.3
scipy==1.11.2

# Data manipulation for keyword analysis
pandas==2.0.3

# Development and Testing Dependencies
pytest==7.4.2
pytest-flask==1.2.0
requests==2.31.0

# Production Server (optional)
gunicorn==21.2.0

# Logging and Monitoring
colorlog==6.7.0

# Security
cryptography==41.0.4

# Data Validation
marshmallow==3.20.1
email-validator==2.0.0

# Performance Monitoring (optional)
flask-limiter==3.5.0

# Configuration Management
configparser==6.0.0

# Memory profiling for NLP operations (optional)
memory-profiler==0.61.0

# Progress bars for batch processing
tqdm==4.66.1

# Caching for NLP models (optional)
joblib==1.3.2

# Text similarity calculations
scikit-learn==1.3.0

# Additional text processing utilities
unidecode==1.3.6

# Word frequency analysis
wordcloud==1.9.2

# Named entity recognition enhancements
spacy-transformers==1.2.5

# Language model downloads (run after installation):
# python -m spacy download en_core_web_sm
# python -c "import nltk; nltk.download('punkt'); nltk.download('stopwords'); nltk.download('averaged_perceptron_tagger'); nltk.download('maxent_ne_chunker'); nltk.download('words')"

# Built-in Python modules (listed for clarity):
# re - Regular expressions (built-in)
# os - Operating system interface (built-in)
# sys - System-specific parameters (built-in)
# datetime - Date and time (built-in)
# json - JSON encoder/decoder (built-in)
# uuid - UUID generation (built-in)
# collections - Specialized container datatypes (built-in)
# typing - Type hints (built-in)
# decimal - Decimal arithmetic (built-in)
