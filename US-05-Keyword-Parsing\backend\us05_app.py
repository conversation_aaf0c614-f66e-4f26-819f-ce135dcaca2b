"""
US-05: Keyword Parsing - Main Flask Application
==============================================

This is the main Flask application for US-05 Keyword Parsing feature.
It integrates with previous US features and provides keyword extraction functionality.

Tech Stack: Flask, Flask-JWT-Extended, SQLAlchemy, PostgreSQL, spaCy/NLTK
Dependencies: US-01 (User Registration), US-02 (JWT Authentication), US-03 (Resume Upload), US-04 (JD Upload)

Usage:
    python us05_app.py

Environment Variables Required:
    - DATABASE_URL or individual DB connection parameters
    - JWT_SECRET_KEY
    - See README_US05.md for complete setup instructions
"""

import os
import sys
from datetime import datetime, timedelta
from flask import Flask, jsonify
from flask_jwt_extended import JWTManager
from flask_cors import CORS

# Add paths for imports from previous US
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-01-User-Registration', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-02-Login-JWT-Token', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-03-Resume-Upload', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-04-JD-Upload', 'backend'))

try:
    from us01_user_model import db, User
    from us02_auth_routes import auth_bp
    from us02_auth_routes import (
        check_if_token_revoked, jwt_additional_claims_loader,
        expired_token_callback, invalid_token_callback, missing_token_callback
    )
    from us03_resume_model import Resume
    from us04_jd_model import JobDescription
    print("✅ Successfully imported from US-01, US-02, US-03, and US-04")
except ImportError as e:
    print(f"⚠️  Import warning: {e}")
    print("Some features may not work without previous US components")
    db = None
    auth_bp = None

# Import US-05 components
try:
    from us05_keyword_model import Keyword
    from us05_processing_routes import keyword_bp
    from us05_keyword_extractor import get_keyword_extractor
    print("✅ Successfully imported US-05 components")
except ImportError as e:
    print(f"❌ Error importing US-05 components: {e}")
    sys.exit(1)

def create_app():
    """
    Create and configure the Flask application for US-05
    
    Returns:
        Flask: Configured Flask application
    """
    app = Flask(__name__)
    
    # Configuration
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production')
    
    # Database configuration
    app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get(
        'DATABASE_URL',
        'postgresql://username:password@localhost/dr_resume_db'
    )
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
    }
    
    # JWT Configuration (from US-02)
    app.config['JWT_SECRET_KEY'] = os.environ.get('JWT_SECRET_KEY', 'jwt-secret-string-change-in-production')
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=1)
    app.config['JWT_REFRESH_TOKEN_EXPIRES'] = timedelta(days=30)
    app.config['JWT_BLACKLIST_ENABLED'] = True
    app.config['JWT_BLACKLIST_TOKEN_CHECKS'] = ['access', 'refresh']
    
    # Initialize extensions
    if db:
        db.init_app(app)
    
    # Initialize JWT
    jwt = JWTManager(app)
    
    # JWT Configuration (from US-02)
    if 'check_if_token_revoked' in globals():
        jwt.token_in_blocklist_loader(check_if_token_revoked)
        jwt.additional_claims_loader(jwt_additional_claims_loader)
        jwt.expired_token_loader(expired_token_callback)
        jwt.invalid_token_loader(invalid_token_callback)
        jwt.unauthorized_loader(missing_token_callback)
    
    # Enable CORS for frontend integration
    CORS(app, origins=['http://localhost:3000', 'http://127.0.0.1:3000'])
    
    # Register blueprints
    if auth_bp:
        app.register_blueprint(auth_bp)
        print("✅ Registered authentication blueprint from US-02")
    
    app.register_blueprint(keyword_bp)
    print("✅ Registered keyword processing blueprint")
    
    # Root endpoint
    @app.route('/')
    def home():
        """Home endpoint with API information"""
        return jsonify({
            'success': True,
            'message': 'Dr. Resume API - US-05: Keyword Parsing',
            'version': '1.0.0',
            'features': {
                'us01_registration': 'active' if auth_bp else 'not_available',
                'us02_jwt_auth': 'active' if auth_bp else 'not_available',
                'us03_resume_upload': 'active' if Resume else 'not_available',
                'us04_jd_upload': 'active' if JobDescription else 'not_available',
                'us05_keyword_parsing': 'active'
            },
            'endpoints': {
                'extract_keywords': 'POST /api/extract_keywords',
                'list_keywords': 'GET /api/keywords',
                'unique_keywords': 'GET /api/keywords/unique',
                'process_all': 'POST /api/process_all',
                'keyword_health': 'GET /api/keyword_health'
            },
            'nlp_capabilities': {
                'extraction_methods': ['spacy', 'nltk', 'hybrid', 'pattern_matching'],
                'keyword_types': ['skill', 'technology', 'experience', 'education', 'certification', 'tool', 'language', 'framework', 'general'],
                'supported_sources': ['resume', 'job_description']
            },
            'authentication': {
                'required': True,
                'type': 'JWT Bearer Token',
                'login_endpoint': '/api/login',
                'register_endpoint': '/api/register'
            },
            'timestamp': datetime.utcnow().isoformat()
        })
    
    # Health check endpoint
    @app.route('/health')
    def health_check():
        """Comprehensive health check for US-05"""
        
        # Check database connection
        db_status = 'unknown'
        user_count = 0
        resume_count = 0
        jd_count = 0
        keyword_count = 0
        
        try:
            if db:
                # Test database connection
                db.session.execute('SELECT 1')
                db_status = 'connected'
                
                # Get counts
                user_count = User.query.count() if User else 0
                resume_count = Resume.query.count() if Resume else 0
                jd_count = JobDescription.query.count() if JobDescription else 0
                keyword_count = Keyword.query.count()
            else:
                db_status = 'not_configured'
        except Exception as e:
            db_status = f'error: {str(e)}'
        
        # Check NLP extractor status
        extractor_status = 'unknown'
        nlp_info = {}
        
        try:
            extractor = get_keyword_extractor()
            extractor_status = 'available'
            nlp_info = {
                'method': extractor.method,
                'spacy_available': extractor.nlp is not None,
                'nltk_available': len(extractor.stop_words) > 0
            }
        except Exception as e:
            extractor_status = f'error: {str(e)}'
        
        return jsonify({
            'success': True,
            'message': 'Application is running',
            'status': {
                'app': 'running',
                'database': db_status,
                'jwt': 'configured',
                'nlp_extractor': extractor_status,
                'feature': 'US-05: Keyword Parsing',
                'user_count': user_count,
                'resume_count': resume_count,
                'jd_count': jd_count,
                'keyword_count': keyword_count
            },
            'features': {
                'us01_registration': 'active' if auth_bp else 'not_available',
                'us02_jwt_auth': 'active' if auth_bp else 'not_available',
                'us03_resume_upload': 'active' if Resume else 'not_available',
                'us04_jd_upload': 'active' if JobDescription else 'not_available',
                'us05_keyword_parsing': 'active'
            },
            'nlp_config': nlp_info,
            'processing_stats': {
                'total_keywords': keyword_count,
                'extraction_methods': ['spacy', 'nltk', 'hybrid', 'pattern_matching'],
                'keyword_types': ['skill', 'technology', 'experience', 'education', 'certification', 'tool', 'language', 'framework', 'general']
            },
            'timestamp': datetime.utcnow().isoformat()
        })
    
    # Global error handlers
    @app.errorhandler(404)
    def not_found(error):
        """Handle 404 errors globally"""
        return jsonify({
            'success': False,
            'message': 'API endpoint not found',
            'error': 'Not Found',
            'available_endpoints': {
                'extract_keywords': 'POST /api/extract_keywords',
                'list_keywords': 'GET /api/keywords',
                'unique_keywords': 'GET /api/keywords/unique',
                'process_all': 'POST /api/process_all',
                'health': 'GET /health',
                'home': 'GET /'
            }
        }), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        """Handle 500 errors globally"""
        if db:
            db.session.rollback()
        return jsonify({
            'success': False,
            'message': 'Internal server error',
            'error': 'Internal Server Error'
        }), 500
    
    @app.errorhandler(400)
    def bad_request(error):
        """Handle 400 errors globally"""
        return jsonify({
            'success': False,
            'message': 'Bad request',
            'error': 'Bad Request'
        }), 400
    
    return app

def main():
    """
    Main function to run the Flask application
    """
    print("🚀 Starting US-05: Keyword Parsing Application")
    print("=" * 60)
    
    # Create Flask app
    app = create_app()
    
    # Create database tables
    if db:
        with app.app_context():
            try:
                db.create_all()
                print("✅ Database tables created/verified")
            except Exception as e:
                print(f"⚠️  Database setup warning: {e}")
    
    # Test NLP components
    print("\n🧠 Testing NLP Components:")
    try:
        extractor = get_keyword_extractor()
        print(f"✅ Keyword extractor initialized with method: {extractor.method}")
        
        # Test extraction with sample text
        sample_text = "Python developer with 5 years experience in Flask and PostgreSQL"
        keywords = extractor.extract_keywords(sample_text, 'resume')
        print(f"✅ Sample extraction successful: {len(keywords)} keywords found")
        
    except Exception as e:
        print(f"⚠️  NLP component warning: {e}")
    
    # Print startup information
    print("\n📋 US-05 Features Available:")
    print("• Automatic Keyword Extraction (spaCy/NLTK)")
    print("• Manual Keyword Processing (POST /api/extract_keywords)")
    print("• Bulk Document Processing (POST /api/process_all)")
    print("• Keyword Management (GET /api/keywords)")
    print("• JWT Authentication (from US-02)")
    print("• User Management (from US-01)")
    
    print("\n🔗 API Endpoints:")
    print("• Home: GET /")
    print("• Health: GET /health")
    print("• Extract Keywords: POST /api/extract_keywords")
    print("• List Keywords: GET /api/keywords")
    print("• Unique Keywords: GET /api/keywords/unique")
    print("• Process All: POST /api/process_all")
    print("• Keyword Health: GET /api/keyword_health")
    
    print("\n🔐 Authentication:")
    print("• Login: POST /api/login")
    print("• Register: POST /api/register")
    print("• All keyword endpoints require JWT authentication")
    
    print("\n🧠 NLP Configuration:")
    print("• Extraction Methods: spaCy, NLTK, Hybrid, Pattern Matching")
    print("• Keyword Types: skill, technology, experience, education, certification")
    print("• Automatic Processing: Runs after resume/JD upload")
    
    print("\n🌐 Starting server...")
    print("Access the API at: http://localhost:5000")
    print("API Documentation: http://localhost:5000/")
    
    # Run the application
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True
    )

if __name__ == '__main__':
    main()
