"""
US-05: Keyword Parsing - Automatic Processing Logic
==================================================

This file contains the automatic keyword extraction logic that integrates with
US-03 (Resume Upload) and US-04 (JD Upload) to automatically process documents
when they are uploaded.

Tech Stack: Python, spaCy/NLTK, SQLAlchemy
Dependencies: US-03 (Resume model), US-04 (JobDescription model), US-05 (Keyword model)
"""

import os
import sys
from datetime import datetime
from typing import Optional, Tuple, List, Dict

# Add paths for imports from previous US
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-01-User-Registration', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-03-Resume-Upload', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-04-JD-Upload', 'backend'))

try:
    from us01_user_model import db
    from us03_resume_model import Resume
    from us04_jd_model import JobDescription
    from us05_keyword_model import Keyword
    from us05_keyword_extractor import get_keyword_extractor
except ImportError as e:
    print(f"⚠️  Import warning: {e}")
    print("Some features may not work without previous US components")

class AutoProcessor:
    """
    Automatic keyword processing for resumes and job descriptions
    
    This class provides methods to automatically extract keywords when
    documents are uploaded through US-03 or US-04.
    """
    
    def __init__(self, extraction_method='hybrid'):
        """
        Initialize the auto processor
        
        Args:
            extraction_method (str): Default extraction method ('spacy', 'nltk', or 'hybrid')
        """
        self.extraction_method = extraction_method
        self.extractor = None
        self._initialize_extractor()
    
    def _initialize_extractor(self):
        """Initialize the keyword extractor"""
        try:
            self.extractor = get_keyword_extractor(method=self.extraction_method)
            print(f"✅ Auto processor initialized with {self.extraction_method} method")
        except Exception as e:
            print(f"⚠️  Auto processor initialization warning: {e}")
    
    def process_resume_after_upload(self, resume_id: str, user_id: str) -> Tuple[bool, str, int]:
        """
        Automatically process a resume after upload
        
        This method is called after a resume is successfully uploaded in US-03
        to extract keywords automatically.
        
        Args:
            resume_id (str): ID of the uploaded resume
            user_id (str): ID of the user who uploaded the resume
            
        Returns:
            Tuple[bool, str, int]: (success, message, keywords_count)
        """
        try:
            # Get the resume
            resume = Resume.query.filter_by(id=resume_id, user_id=user_id).first()
            if not resume:
                return False, "Resume not found", 0
            
            # Check if already processed
            if resume.keywords_extracted:
                return True, "Resume already processed", 0
            
            # Check if text is available
            if not resume.extracted_text:
                return False, "Resume text not available for processing", 0
            
            # Extract keywords
            if not self.extractor:
                return False, "Keyword extractor not available", 0
            
            keywords = self.extractor.extract_keywords(resume.extracted_text, 'resume')
            
            if not keywords:
                # Mark as processed even if no keywords found
                resume.keywords_extracted = True
                resume.is_processed = True
                resume.updated_at = datetime.utcnow()
                db.session.commit()
                return True, "No keywords found in resume", 0
            
            # Save keywords to database
            saved_count = 0
            for kw_data in keywords:
                keyword_obj, error = Keyword.create_keyword(
                    user_id=user_id,
                    resume_id=resume_id,
                    keyword=kw_data['keyword'],
                    keyword_type=kw_data['keyword_type'],
                    frequency=kw_data.get('frequency', 1),
                    confidence_score=kw_data['confidence_score'],
                    context_snippet=kw_data.get('context_snippet'),
                    position_in_text=kw_data.get('position_in_text'),
                    extraction_method=kw_data['extraction_method']
                )
                
                if keyword_obj:
                    saved_count += 1
                else:
                    print(f"Error saving keyword: {error}")
            
            # Update resume status
            resume.keywords_extracted = True
            resume.is_processed = True
            resume.updated_at = datetime.utcnow()
            db.session.commit()
            
            return True, f"Successfully extracted {saved_count} keywords", saved_count
            
        except Exception as e:
            db.session.rollback()
            return False, f"Error processing resume: {str(e)}", 0
    
    def process_job_description_after_upload(self, jd_id: str, user_id: str) -> Tuple[bool, str, int]:
        """
        Automatically process a job description after upload
        
        This method is called after a job description is successfully uploaded in US-04
        to extract keywords automatically.
        
        Args:
            jd_id (str): ID of the uploaded job description
            user_id (str): ID of the user who uploaded the job description
            
        Returns:
            Tuple[bool, str, int]: (success, message, keywords_count)
        """
        try:
            # Get the job description
            jd = JobDescription.query.filter_by(id=jd_id, user_id=user_id).first()
            if not jd:
                return False, "Job description not found", 0
            
            # Check if already processed
            if jd.keywords_extracted:
                return True, "Job description already processed", 0
            
            # Extract keywords
            if not self.extractor:
                return False, "Keyword extractor not available", 0
            
            keywords = self.extractor.extract_keywords(jd.job_description_text, 'job_description')
            
            if not keywords:
                # Mark as processed even if no keywords found
                jd.keywords_extracted = True
                jd.is_processed = True
                jd.updated_at = datetime.utcnow()
                db.session.commit()
                return True, "No keywords found in job description", 0
            
            # Save keywords to database
            saved_count = 0
            for kw_data in keywords:
                keyword_obj, error = Keyword.create_keyword(
                    user_id=user_id,
                    job_description_id=jd_id,
                    keyword=kw_data['keyword'],
                    keyword_type=kw_data['keyword_type'],
                    frequency=kw_data.get('frequency', 1),
                    confidence_score=kw_data['confidence_score'],
                    context_snippet=kw_data.get('context_snippet'),
                    position_in_text=kw_data.get('position_in_text'),
                    extraction_method=kw_data['extraction_method']
                )
                
                if keyword_obj:
                    saved_count += 1
                else:
                    print(f"Error saving keyword: {error}")
            
            # Update job description status
            jd.keywords_extracted = True
            jd.is_processed = True
            jd.updated_at = datetime.utcnow()
            db.session.commit()
            
            return True, f"Successfully extracted {saved_count} keywords", saved_count
            
        except Exception as e:
            db.session.rollback()
            return False, f"Error processing job description: {str(e)}", 0
    
    def process_all_unprocessed_documents(self, user_id: str) -> Dict[str, any]:
        """
        Process all unprocessed documents for a user
        
        Args:
            user_id (str): ID of the user
            
        Returns:
            Dict: Processing results summary
        """
        results = {
            'resumes_processed': 0,
            'jds_processed': 0,
            'total_keywords_extracted': 0,
            'errors': []
        }
        
        try:
            # Process unprocessed resumes
            unprocessed_resumes = Resume.query.filter_by(
                user_id=user_id,
                keywords_extracted=False
            ).all()
            
            for resume in unprocessed_resumes:
                if resume.extracted_text:  # Only process if text is available
                    success, message, count = self.process_resume_after_upload(resume.id, user_id)
                    if success:
                        results['resumes_processed'] += 1
                        results['total_keywords_extracted'] += count
                    else:
                        results['errors'].append(f"Resume {resume.id}: {message}")
            
            # Process unprocessed job descriptions
            unprocessed_jds = JobDescription.query.filter_by(
                user_id=user_id,
                keywords_extracted=False
            ).all()
            
            for jd in unprocessed_jds:
                success, message, count = self.process_job_description_after_upload(jd.id, user_id)
                if success:
                    results['jds_processed'] += 1
                    results['total_keywords_extracted'] += count
                else:
                    results['errors'].append(f"Job Description {jd.id}: {message}")
            
            return results
            
        except Exception as e:
            results['errors'].append(f"Batch processing error: {str(e)}")
            return results
    
    def reprocess_document(self, document_id: str, user_id: str, document_type: str) -> Tuple[bool, str, int]:
        """
        Reprocess a document (force re-extraction)
        
        Args:
            document_id (str): ID of the document
            user_id (str): ID of the user
            document_type (str): 'resume' or 'job_description'
            
        Returns:
            Tuple[bool, str, int]: (success, message, keywords_count)
        """
        try:
            # Clear existing keywords
            if document_type == 'resume':
                Keyword.query.filter_by(user_id=user_id, resume_id=document_id).delete()
                # Reset processing status
                resume = Resume.query.filter_by(id=document_id, user_id=user_id).first()
                if resume:
                    resume.keywords_extracted = False
                    resume.is_processed = False
                db.session.commit()
                # Process again
                return self.process_resume_after_upload(document_id, user_id)
                
            elif document_type == 'job_description':
                Keyword.query.filter_by(user_id=user_id, job_description_id=document_id).delete()
                # Reset processing status
                jd = JobDescription.query.filter_by(id=document_id, user_id=user_id).first()
                if jd:
                    jd.keywords_extracted = False
                    jd.is_processed = False
                db.session.commit()
                # Process again
                return self.process_job_description_after_upload(document_id, user_id)
            
            else:
                return False, "Invalid document type", 0
                
        except Exception as e:
            db.session.rollback()
            return False, f"Error reprocessing document: {str(e)}", 0
    
    def get_processing_status(self, user_id: str) -> Dict[str, any]:
        """
        Get processing status for all user documents
        
        Args:
            user_id (str): ID of the user
            
        Returns:
            Dict: Processing status summary
        """
        try:
            # Count resumes
            total_resumes = Resume.query.filter_by(user_id=user_id).count()
            processed_resumes = Resume.query.filter_by(user_id=user_id, keywords_extracted=True).count()
            
            # Count job descriptions
            total_jds = JobDescription.query.filter_by(user_id=user_id).count()
            processed_jds = JobDescription.query.filter_by(user_id=user_id, keywords_extracted=True).count()
            
            # Count keywords
            total_keywords = Keyword.query.filter_by(user_id=user_id).count()
            
            return {
                'resumes': {
                    'total': total_resumes,
                    'processed': processed_resumes,
                    'pending': total_resumes - processed_resumes
                },
                'job_descriptions': {
                    'total': total_jds,
                    'processed': processed_jds,
                    'pending': total_jds - processed_jds
                },
                'keywords': {
                    'total': total_keywords
                },
                'overall_progress': {
                    'total_documents': total_resumes + total_jds,
                    'processed_documents': processed_resumes + processed_jds,
                    'completion_percentage': round(
                        ((processed_resumes + processed_jds) / max(1, total_resumes + total_jds)) * 100, 2
                    )
                }
            }
            
        except Exception as e:
            return {
                'error': f"Error getting processing status: {str(e)}"
            }

# Global auto processor instance
_auto_processor_instance = None

def get_auto_processor(extraction_method='hybrid'):
    """
    Get a singleton instance of AutoProcessor
    
    Args:
        extraction_method (str): Extraction method ('spacy', 'nltk', or 'hybrid')
        
    Returns:
        AutoProcessor: Configured auto processor instance
    """
    global _auto_processor_instance
    
    if _auto_processor_instance is None:
        _auto_processor_instance = AutoProcessor(extraction_method=extraction_method)
    
    return _auto_processor_instance

# Integration functions for US-03 and US-04
def auto_process_resume(resume_id: str, user_id: str) -> Tuple[bool, str, int]:
    """
    Integration function for US-03 Resume Upload
    
    Call this function after a resume is successfully uploaded and processed
    to automatically extract keywords.
    
    Args:
        resume_id (str): ID of the uploaded resume
        user_id (str): ID of the user
        
    Returns:
        Tuple[bool, str, int]: (success, message, keywords_count)
    """
    processor = get_auto_processor()
    return processor.process_resume_after_upload(resume_id, user_id)

def auto_process_job_description(jd_id: str, user_id: str) -> Tuple[bool, str, int]:
    """
    Integration function for US-04 Job Description Upload
    
    Call this function after a job description is successfully uploaded
    to automatically extract keywords.
    
    Args:
        jd_id (str): ID of the uploaded job description
        user_id (str): ID of the user
        
    Returns:
        Tuple[bool, str, int]: (success, message, keywords_count)
    """
    processor = get_auto_processor()
    return processor.process_job_description_after_upload(jd_id, user_id)
