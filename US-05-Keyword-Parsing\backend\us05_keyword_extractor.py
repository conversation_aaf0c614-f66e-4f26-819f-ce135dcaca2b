"""
US-05: Keyword Parsing - Keyword Extraction Utility
==================================================

This file contains the keyword extraction logic using spaCy and NLTK.
It processes text from resumes and job descriptions to extract meaningful keywords.

Tech Stack: spaCy, NLTK, Python
Dependencies: spaCy models, NLTK data
"""

import re
import os
import sys
from typing import List, Dict, Tuple, Optional
from collections import Counter

# NLP Libraries
try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False
    print("⚠️  spaCy not available. Install with: pip install spacy")

try:
    import nltk
    from nltk.corpus import stopwords
    from nltk.tokenize import word_tokenize, sent_tokenize
    from nltk.tag import pos_tag
    from nltk.chunk import ne_chunk
    NLTK_AVAILABLE = True
except ImportError:
    NLTK_AVAILABLE = False
    print("⚠️  NLTK not available. Install with: pip install nltk")

class KeywordExtractor:
    """
    Keyword extraction utility using spaCy and NLTK
    
    This class provides methods to extract keywords from text using various NLP techniques:
    - Named Entity Recognition (NER)
    - Part-of-Speech (POS) tagging
    - Custom pattern matching for technical terms
    - Frequency analysis
    """
    
    def __init__(self, method='spacy', language='en'):
        """
        Initialize the keyword extractor
        
        Args:
            method (str): Extraction method ('spacy', 'nltk', or 'hybrid')
            language (str): Language code (default: 'en')
        """
        self.method = method
        self.language = language
        self.nlp = None
        self.stop_words = set()
        
        # Initialize NLP models
        self._initialize_models()
        
        # Define keyword categories and patterns
        self._initialize_patterns()
    
    def _initialize_models(self):
        """Initialize spaCy and NLTK models"""
        
        if self.method in ['spacy', 'hybrid'] and SPACY_AVAILABLE:
            try:
                # Try to load English model
                self.nlp = spacy.load("en_core_web_sm")
                print("✅ spaCy English model loaded successfully")
            except OSError:
                print("⚠️  spaCy English model not found. Install with: python -m spacy download en_core_web_sm")
                if self.method == 'spacy':
                    self.method = 'nltk'  # Fallback to NLTK
        
        if self.method in ['nltk', 'hybrid'] and NLTK_AVAILABLE:
            try:
                # Download required NLTK data if not present
                try:
                    nltk.data.find('tokenizers/punkt')
                except LookupError:
                    print("Downloading NLTK punkt tokenizer...")
                    nltk.download('punkt')
                
                try:
                    nltk.data.find('corpora/stopwords')
                    self.stop_words = set(stopwords.words('english'))
                except LookupError:
                    print("Downloading NLTK stopwords...")
                    nltk.download('stopwords')
                    self.stop_words = set(stopwords.words('english'))
                
                try:
                    nltk.data.find('taggers/averaged_perceptron_tagger')
                except LookupError:
                    print("Downloading NLTK POS tagger...")
                    nltk.download('averaged_perceptron_tagger')
                
                try:
                    nltk.data.find('chunkers/maxent_ne_chunker')
                except LookupError:
                    print("Downloading NLTK named entity chunker...")
                    nltk.download('maxent_ne_chunker')
                    nltk.download('words')
                
                print("✅ NLTK components initialized successfully")
                
            except Exception as e:
                print(f"⚠️  NLTK initialization error: {e}")
    
    def _initialize_patterns(self):
        """Initialize keyword patterns and categories"""
        
        # Technical skills patterns
        self.tech_patterns = {
            'programming_languages': [
                r'\b(?:python|java|javascript|c\+\+|c#|php|ruby|go|rust|swift|kotlin|scala|r|matlab)\b',
                r'\b(?:html|css|sql|typescript|perl|bash|powershell)\b'
            ],
            'frameworks': [
                r'\b(?:react|angular|vue|django|flask|spring|express|laravel|rails)\b',
                r'\b(?:bootstrap|jquery|node\.?js|next\.?js|nuxt\.?js)\b'
            ],
            'databases': [
                r'\b(?:mysql|postgresql|mongodb|redis|elasticsearch|sqlite|oracle|sql\s+server)\b',
                r'\b(?:dynamodb|cassandra|neo4j|influxdb)\b'
            ],
            'cloud_platforms': [
                r'\b(?:aws|azure|gcp|google\s+cloud|amazon\s+web\s+services)\b',
                r'\b(?:docker|kubernetes|terraform|ansible)\b'
            ],
            'tools': [
                r'\b(?:git|github|gitlab|bitbucket|jenkins|travis|circleci)\b',
                r'\b(?:jira|confluence|slack|teams|zoom)\b'
            ]
        }
        
        # Experience level patterns
        self.experience_patterns = [
            r'\b(?:senior|lead|principal|staff|architect|manager|director|vp|cto|ceo)\b',
            r'\b(?:junior|entry\s+level|associate|intern|trainee)\b',
            r'\b(?:\d+\+?\s+years?|years?\s+of\s+experience)\b'
        ]
        
        # Education patterns
        self.education_patterns = [
            r'\b(?:bachelor|master|phd|doctorate|mba|bs|ms|ba|ma)\b',
            r'\b(?:computer\s+science|software\s+engineering|information\s+technology)\b',
            r'\b(?:university|college|institute|school)\b'
        ]
        
        # Certification patterns
        self.certification_patterns = [
            r'\b(?:aws\s+certified|azure\s+certified|google\s+certified)\b',
            r'\b(?:pmp|scrum\s+master|agile|cissp|cisa|cism)\b'
        ]
    
    def extract_keywords(self, text: str, source_type: str = 'general') -> List[Dict]:
        """
        Extract keywords from text using the configured method
        
        Args:
            text (str): Input text to process
            source_type (str): Type of source ('resume' or 'job_description')
            
        Returns:
            List[Dict]: List of extracted keywords with metadata
        """
        if not text or not text.strip():
            return []
        
        # Clean and preprocess text
        cleaned_text = self._preprocess_text(text)
        
        # Extract keywords based on method
        if self.method == 'spacy' and self.nlp:
            keywords = self._extract_with_spacy(cleaned_text, source_type)
        elif self.method == 'nltk' and NLTK_AVAILABLE:
            keywords = self._extract_with_nltk(cleaned_text, source_type)
        elif self.method == 'hybrid':
            keywords = self._extract_hybrid(cleaned_text, source_type)
        else:
            # Fallback to pattern-based extraction
            keywords = self._extract_with_patterns(cleaned_text, source_type)
        
        # Post-process and rank keywords
        processed_keywords = self._post_process_keywords(keywords, text)
        
        return processed_keywords
    
    def _preprocess_text(self, text: str) -> str:
        """
        Clean and preprocess text for keyword extraction
        
        Args:
            text (str): Raw input text
            
        Returns:
            str: Cleaned text
        """
        # Remove extra whitespace and normalize
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove special characters but keep important punctuation
        text = re.sub(r'[^\w\s\.\-\+\#]', ' ', text)
        
        # Normalize common variations
        text = re.sub(r'\bC\+\+\b', 'cpp', text)
        text = re.sub(r'\bC#\b', 'csharp', text)
        text = re.sub(r'\bNode\.js\b', 'nodejs', text, flags=re.IGNORECASE)
        text = re.sub(r'\bReact\.js\b', 'react', text, flags=re.IGNORECASE)
        
        return text
    
    def _extract_with_spacy(self, text: str, source_type: str) -> List[Dict]:
        """
        Extract keywords using spaCy NLP
        
        Args:
            text (str): Preprocessed text
            source_type (str): Source type
            
        Returns:
            List[Dict]: Extracted keywords
        """
        keywords = []
        doc = self.nlp(text)
        
        # Extract named entities
        for ent in doc.ents:
            if ent.label_ in ['ORG', 'PRODUCT', 'LANGUAGE', 'SKILL']:
                keywords.append({
                    'keyword': ent.text.lower().strip(),
                    'keyword_type': self._classify_keyword_type(ent.text, ent.label_),
                    'confidence_score': 0.85,
                    'context_snippet': self._get_context(text, ent.start_char, ent.end_char),
                    'position_in_text': ent.start_char,
                    'extraction_method': 'spacy_ner'
                })
        
        # Extract noun phrases
        for chunk in doc.noun_chunks:
            if len(chunk.text.split()) <= 3 and chunk.text.lower() not in self.stop_words:
                keywords.append({
                    'keyword': chunk.text.lower().strip(),
                    'keyword_type': self._classify_keyword_type(chunk.text),
                    'confidence_score': 0.70,
                    'context_snippet': self._get_context(text, chunk.start_char, chunk.end_char),
                    'position_in_text': chunk.start_char,
                    'extraction_method': 'spacy_noun_chunks'
                })
        
        # Extract technical terms using patterns
        pattern_keywords = self._extract_with_patterns(text, source_type)
        keywords.extend(pattern_keywords)
        
        return keywords
    
    def _extract_with_nltk(self, text: str, source_type: str) -> List[Dict]:
        """
        Extract keywords using NLTK
        
        Args:
            text (str): Preprocessed text
            source_type (str): Source type
            
        Returns:
            List[Dict]: Extracted keywords
        """
        keywords = []
        
        # Tokenize and tag
        tokens = word_tokenize(text)
        pos_tags = pos_tag(tokens)
        
        # Extract nouns and adjectives
        for word, pos in pos_tags:
            if (pos.startswith('NN') or pos.startswith('JJ')) and len(word) > 2:
                if word.lower() not in self.stop_words:
                    keywords.append({
                        'keyword': word.lower(),
                        'keyword_type': self._classify_keyword_type(word),
                        'confidence_score': 0.65,
                        'context_snippet': None,
                        'position_in_text': text.lower().find(word.lower()),
                        'extraction_method': 'nltk_pos'
                    })
        
        # Extract named entities
        try:
            tree = ne_chunk(pos_tags)
            for subtree in tree:
                if hasattr(subtree, 'label'):
                    entity = ' '.join([token for token, pos in subtree.leaves()])
                    keywords.append({
                        'keyword': entity.lower(),
                        'keyword_type': self._classify_keyword_type(entity, subtree.label()),
                        'confidence_score': 0.75,
                        'context_snippet': None,
                        'position_in_text': text.lower().find(entity.lower()),
                        'extraction_method': 'nltk_ner'
                    })
        except Exception as e:
            print(f"NLTK NER error: {e}")
        
        # Extract technical terms using patterns
        pattern_keywords = self._extract_with_patterns(text, source_type)
        keywords.extend(pattern_keywords)
        
        return keywords
    
    def _extract_hybrid(self, text: str, source_type: str) -> List[Dict]:
        """
        Extract keywords using both spaCy and NLTK
        
        Args:
            text (str): Preprocessed text
            source_type (str): Source type
            
        Returns:
            List[Dict]: Extracted keywords
        """
        keywords = []
        
        # Use spaCy if available
        if self.nlp:
            spacy_keywords = self._extract_with_spacy(text, source_type)
            keywords.extend(spacy_keywords)
        
        # Use NLTK if available
        if NLTK_AVAILABLE:
            nltk_keywords = self._extract_with_nltk(text, source_type)
            keywords.extend(nltk_keywords)
        
        # If neither is available, use patterns only
        if not keywords:
            keywords = self._extract_with_patterns(text, source_type)
        
        return keywords
    
    def _extract_with_patterns(self, text: str, source_type: str) -> List[Dict]:
        """
        Extract keywords using regex patterns
        
        Args:
            text (str): Preprocessed text
            source_type (str): Source type
            
        Returns:
            List[Dict]: Extracted keywords
        """
        keywords = []
        text_lower = text.lower()
        
        # Extract technical skills
        for category, patterns in self.tech_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, text_lower, re.IGNORECASE)
                for match in matches:
                    keyword_text = match.group().strip()
                    keywords.append({
                        'keyword': keyword_text,
                        'keyword_type': 'technology' if category in ['programming_languages', 'databases'] else 'tool',
                        'confidence_score': 0.90,
                        'context_snippet': self._get_context(text, match.start(), match.end()),
                        'position_in_text': match.start(),
                        'extraction_method': 'pattern_matching'
                    })
        
        # Extract experience terms
        for pattern in self.experience_patterns:
            matches = re.finditer(pattern, text_lower, re.IGNORECASE)
            for match in matches:
                keywords.append({
                    'keyword': match.group().strip(),
                    'keyword_type': 'experience',
                    'confidence_score': 0.85,
                    'context_snippet': self._get_context(text, match.start(), match.end()),
                    'position_in_text': match.start(),
                    'extraction_method': 'pattern_matching'
                })
        
        # Extract education terms
        for pattern in self.education_patterns:
            matches = re.finditer(pattern, text_lower, re.IGNORECASE)
            for match in matches:
                keywords.append({
                    'keyword': match.group().strip(),
                    'keyword_type': 'education',
                    'confidence_score': 0.80,
                    'context_snippet': self._get_context(text, match.start(), match.end()),
                    'position_in_text': match.start(),
                    'extraction_method': 'pattern_matching'
                })
        
        return keywords
    
    def _classify_keyword_type(self, keyword: str, ner_label: str = None) -> str:
        """
        Classify keyword type based on content and NER label
        
        Args:
            keyword (str): Keyword text
            ner_label (str, optional): Named entity label
            
        Returns:
            str: Keyword type
        """
        keyword_lower = keyword.lower()
        
        # Check technical patterns
        for category, patterns in self.tech_patterns.items():
            for pattern in patterns:
                if re.search(pattern, keyword_lower):
                    if category == 'programming_languages':
                        return 'technology'
                    elif category == 'frameworks':
                        return 'framework'
                    elif category == 'databases':
                        return 'technology'
                    else:
                        return 'tool'
        
        # Check experience patterns
        for pattern in self.experience_patterns:
            if re.search(pattern, keyword_lower):
                return 'experience'
        
        # Check education patterns
        for pattern in self.education_patterns:
            if re.search(pattern, keyword_lower):
                return 'education'
        
        # Check certification patterns
        for pattern in self.certification_patterns:
            if re.search(pattern, keyword_lower):
                return 'certification'
        
        # Use NER label if available
        if ner_label:
            if ner_label in ['ORG']:
                return 'general'
            elif ner_label in ['PRODUCT', 'LANGUAGE']:
                return 'technology'
        
        return 'general'
    
    def _get_context(self, text: str, start: int, end: int, window: int = 50) -> str:
        """
        Get context snippet around a keyword
        
        Args:
            text (str): Full text
            start (int): Start position of keyword
            end (int): End position of keyword
            window (int): Context window size
            
        Returns:
            str: Context snippet
        """
        context_start = max(0, start - window)
        context_end = min(len(text), end + window)
        return text[context_start:context_end].strip()
    
    def _post_process_keywords(self, keywords: List[Dict], original_text: str) -> List[Dict]:
        """
        Post-process and rank extracted keywords
        
        Args:
            keywords (List[Dict]): Raw extracted keywords
            original_text (str): Original text for frequency calculation
            
        Returns:
            List[Dict]: Processed and ranked keywords
        """
        # Remove duplicates and calculate frequency
        keyword_map = {}
        
        for kw in keywords:
            key = kw['keyword'].lower().strip()
            if len(key) < 2:  # Skip very short keywords
                continue
            
            if key in keyword_map:
                # Update frequency and keep highest confidence
                keyword_map[key]['frequency'] += 1
                if kw['confidence_score'] > keyword_map[key]['confidence_score']:
                    keyword_map[key]['confidence_score'] = kw['confidence_score']
                    keyword_map[key]['extraction_method'] = kw['extraction_method']
            else:
                kw['frequency'] = original_text.lower().count(key)
                keyword_map[key] = kw
        
        # Convert back to list and sort by confidence and frequency
        processed_keywords = list(keyword_map.values())
        processed_keywords.sort(key=lambda x: (x['confidence_score'], x['frequency']), reverse=True)
        
        return processed_keywords

# Global extractor instance
_extractor_instance = None

def get_keyword_extractor(method='hybrid'):
    """
    Get a singleton instance of KeywordExtractor

    Args:
        method (str): Extraction method ('spacy', 'nltk', or 'hybrid')

    Returns:
        KeywordExtractor: Configured extractor instance
    """
    global _extractor_instance

    if _extractor_instance is None:
        _extractor_instance = KeywordExtractor(method=method)

    return _extractor_instance
