"""
US-05: Keyword Parsing - Keyword Model
=====================================

This file contains the SQLAlchemy model for keywords extracted from resumes and job descriptions.
It handles keyword data storage, validation, and relationships.

Tech Stack: SQLAlchemy, PostgreSQL
Dependencies: US-01 (User model), US-03 (Resume model), US-04 (JobDescription model)
"""

import os
import sys
import uuid
from datetime import datetime
from decimal import Decimal

# Add paths for imports from previous US
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-01-User-Registration', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-03-Resume-Upload', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-04-JD-Upload', 'backend'))

try:
    from us01_user_model import db, User
except ImportError as e:
    print(f"⚠️  Import warning: {e}")
    print("Some features may not work without US-01")
    from flask_sqlalchemy import SQLAlchemy
    db = SQLAlchemy()

try:
    from us03_resume_model import Resume
except ImportError as e:
    print(f"⚠️  Import warning: {e}")
    print("Resume-related features may not work without US-03")
    Resume = None

try:
    from us04_jd_model import JobDescription
except ImportError as e:
    print(f"⚠️  Import warning: {e}")
    print("Job description-related features may not work without US-04")
    JobDescription = None

class Keyword(db.Model):
    """
    Keyword Model for US-05 Keyword Parsing
    
    This model stores extracted keywords from resumes and job descriptions including:
    - Unique keyword ID (UUID)
    - User ID (foreign key to users table)
    - Source document references (resume_id OR job_description_id)
    - Keyword information (text, type, frequency, confidence)
    - Context and processing metadata
    - Timestamps for tracking
    """
    
    __tablename__ = 'keywords'
    
    # Primary key - using UUID for better security
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Foreign key to users table (from US-01)
    user_id = db.Column(db.String(36), db.ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    
    # Source document references (one of these will be set)
    resume_id = db.Column(db.String(36), db.ForeignKey('resumes.id', ondelete='CASCADE'), nullable=True)
    job_description_id = db.Column(db.String(36), db.ForeignKey('job_descriptions.id', ondelete='CASCADE'), nullable=True)
    
    # Keyword information
    keyword = db.Column(db.String(100), nullable=False)
    keyword_type = db.Column(db.String(50), default='general', nullable=True)
    frequency = db.Column(db.Integer, default=1, nullable=False)
    confidence_score = db.Column(db.Numeric(3, 2), default=Decimal('0.80'), nullable=False)
    
    # Context information
    context_snippet = db.Column(db.Text, nullable=True)
    position_in_text = db.Column(db.Integer, nullable=True)
    
    # Processing metadata
    extraction_method = db.Column(db.String(50), default='spacy', nullable=True)
    extraction_version = db.Column(db.String(20), default='1.0', nullable=True)
    is_verified = db.Column(db.Boolean, default=False, nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = db.relationship('User', backref=db.backref('keywords', lazy=True, cascade='all, delete-orphan'))
    
    if Resume:
        resume = db.relationship('Resume', backref=db.backref('keywords', lazy=True, cascade='all, delete-orphan'))
    
    if JobDescription:
        job_description = db.relationship('JobDescription', backref=db.backref('keywords', lazy=True, cascade='all, delete-orphan'))
    
    def __init__(self, user_id, keyword, keyword_type='general', frequency=1, 
                 confidence_score=0.80, resume_id=None, job_description_id=None,
                 context_snippet=None, position_in_text=None, extraction_method='spacy',
                 extraction_version='1.0'):
        """
        Initialize a new keyword
        
        Args:
            user_id (str): ID of the user who owns this keyword
            keyword (str): The extracted keyword text
            keyword_type (str, optional): Type of keyword (default: 'general')
            frequency (int, optional): Frequency of occurrence (default: 1)
            confidence_score (float, optional): NLP confidence score (default: 0.80)
            resume_id (str, optional): ID of source resume
            job_description_id (str, optional): ID of source job description
            context_snippet (str, optional): Surrounding text context
            position_in_text (int, optional): Character position in original text
            extraction_method (str, optional): Extraction method used (default: 'spacy')
            extraction_version (str, optional): Version of extraction algorithm (default: '1.0')
        """
        self.user_id = user_id
        self.keyword = keyword.strip().lower()  # Normalize keyword
        self.keyword_type = keyword_type
        self.frequency = frequency
        self.confidence_score = Decimal(str(confidence_score))
        self.resume_id = resume_id
        self.job_description_id = job_description_id
        self.context_snippet = context_snippet
        self.position_in_text = position_in_text
        self.extraction_method = extraction_method
        self.extraction_version = extraction_version
    
    def to_dict(self):
        """
        Convert keyword to dictionary for JSON serialization
        
        Returns:
            dict: Keyword data as dictionary
        """
        return {
            'id': self.id,
            'user_id': self.user_id,
            'resume_id': self.resume_id,
            'job_description_id': self.job_description_id,
            'keyword': self.keyword,
            'keyword_type': self.keyword_type,
            'frequency': self.frequency,
            'confidence_score': float(self.confidence_score),
            'context_snippet': self.context_snippet,
            'position_in_text': self.position_in_text,
            'extraction_method': self.extraction_method,
            'extraction_version': self.extraction_version,
            'is_verified': self.is_verified,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'source_type': 'resume' if self.resume_id else 'job_description' if self.job_description_id else 'unknown'
        }
    
    @classmethod
    def create_keyword(cls, user_id, keyword, **kwargs):
        """
        Create a new keyword with validation
        
        Args:
            user_id (str): ID of the user
            keyword (str): The keyword text
            **kwargs: Additional keyword fields
            
        Returns:
            tuple: (Keyword object, error_message)
                   Returns (keyword, None) on success
                   Returns (None, error_message) on failure
        """
        try:
            # Validate required fields
            if not keyword or not keyword.strip():
                return None, "Keyword text is required"
            
            if not user_id:
                return None, "User ID is required"
            
            # Validate user exists
            user = User.query.get(user_id)
            if not user:
                return None, "User not found"
            
            # Validate source document
            resume_id = kwargs.get('resume_id')
            job_description_id = kwargs.get('job_description_id')
            
            if not resume_id and not job_description_id:
                return None, "Either resume_id or job_description_id must be provided"
            
            if resume_id and job_description_id:
                return None, "Cannot specify both resume_id and job_description_id"
            
            # Validate source document exists and belongs to user
            if resume_id and Resume:
                resume = Resume.query.filter_by(id=resume_id, user_id=user_id).first()
                if not resume:
                    return None, "Resume not found or does not belong to user"
            
            if job_description_id and JobDescription:
                jd = JobDescription.query.filter_by(id=job_description_id, user_id=user_id).first()
                if not jd:
                    return None, "Job description not found or does not belong to user"
            
            # Validate keyword type
            valid_types = ['skill', 'technology', 'experience', 'education', 'certification', 
                          'tool', 'language', 'framework', 'general']
            keyword_type = kwargs.get('keyword_type', 'general')
            if keyword_type not in valid_types:
                return None, f"Invalid keyword type. Must be one of: {', '.join(valid_types)}"
            
            # Validate confidence score
            confidence_score = kwargs.get('confidence_score', 0.80)
            if not (0.0 <= confidence_score <= 1.0):
                return None, "Confidence score must be between 0.0 and 1.0"
            
            # Validate frequency
            frequency = kwargs.get('frequency', 1)
            if frequency < 1:
                return None, "Frequency must be at least 1"
            
            # Create keyword
            keyword_obj = cls(
                user_id=user_id,
                keyword=keyword,
                **kwargs
            )
            
            # Save to database
            db.session.add(keyword_obj)
            db.session.commit()
            
            return keyword_obj, None
            
        except Exception as e:
            db.session.rollback()
            return None, f"Error creating keyword: {str(e)}"
    
    @classmethod
    def get_by_document(cls, user_id, resume_id=None, job_description_id=None, keyword_type=None):
        """
        Get keywords for a specific document
        
        Args:
            user_id (str): User ID
            resume_id (str, optional): Resume ID
            job_description_id (str, optional): Job description ID
            keyword_type (str, optional): Filter by keyword type
            
        Returns:
            list: List of Keyword objects
        """
        query = cls.query.filter_by(user_id=user_id)
        
        if resume_id:
            query = query.filter_by(resume_id=resume_id)
        
        if job_description_id:
            query = query.filter_by(job_description_id=job_description_id)
        
        if keyword_type:
            query = query.filter_by(keyword_type=keyword_type)
        
        return query.order_by(cls.confidence_score.desc(), cls.frequency.desc()).all()
    
    @classmethod
    def get_by_user(cls, user_id, limit=None):
        """
        Get keywords for a specific user
        
        Args:
            user_id (str): User ID
            limit (int, optional): Maximum number of results
            
        Returns:
            list: List of Keyword objects
        """
        query = cls.query.filter_by(user_id=user_id).order_by(cls.created_at.desc())
        
        if limit:
            query = query.limit(limit)
            
        return query.all()
    
    @classmethod
    def get_unique_keywords(cls, user_id, resume_id=None, job_description_id=None):
        """
        Get unique keywords (deduplicated) for a document
        
        Args:
            user_id (str): User ID
            resume_id (str, optional): Resume ID
            job_description_id (str, optional): Job description ID
            
        Returns:
            list: List of unique keyword strings
        """
        query = cls.query.filter_by(user_id=user_id)
        
        if resume_id:
            query = query.filter_by(resume_id=resume_id)
        
        if job_description_id:
            query = query.filter_by(job_description_id=job_description_id)
        
        keywords = query.with_entities(cls.keyword).distinct().all()
        return [kw[0] for kw in keywords]
    
    @classmethod
    def bulk_create_keywords(cls, keywords_data):
        """
        Create multiple keywords in bulk
        
        Args:
            keywords_data (list): List of keyword dictionaries
            
        Returns:
            tuple: (success_count, error_messages)
        """
        success_count = 0
        error_messages = []
        
        try:
            for kw_data in keywords_data:
                keyword, error = cls.create_keyword(**kw_data)
                if keyword:
                    success_count += 1
                else:
                    error_messages.append(f"Keyword '{kw_data.get('keyword', 'unknown')}': {error}")
            
            return success_count, error_messages
            
        except Exception as e:
            db.session.rollback()
            return 0, [f"Bulk creation error: {str(e)}"]
    
    def update_confidence(self, new_confidence):
        """
        Update the confidence score for this keyword
        
        Args:
            new_confidence (float): New confidence score (0.0-1.0)
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not (0.0 <= new_confidence <= 1.0):
                return False
            
            self.confidence_score = Decimal(str(new_confidence))
            self.updated_at = datetime.utcnow()
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error updating confidence: {e}")
            return False
    
    def verify_keyword(self, is_verified=True):
        """
        Mark this keyword as manually verified
        
        Args:
            is_verified (bool): Verification status
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.is_verified = is_verified
            self.updated_at = datetime.utcnow()
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error updating verification: {e}")
            return False
    
    def delete(self):
        """
        Delete this keyword
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            db.session.delete(self)
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error deleting keyword: {e}")
            return False
    
    def __repr__(self):
        """String representation of the keyword"""
        source = f"resume:{self.resume_id}" if self.resume_id else f"jd:{self.job_description_id}"
        return f'<Keyword {self.id}: "{self.keyword}" ({self.keyword_type}) from {source}>'
