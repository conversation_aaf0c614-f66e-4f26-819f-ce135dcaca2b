"""
US-05: Keyword Parsing - Processing Routes
=========================================

This file contains the Flask routes for keyword extraction and processing.
It handles automatic keyword extraction from resumes and job descriptions.

Tech Stack: Flask, Flask-JWT-Extended, SQLAlchemy, spaCy/NLTK
Dependencies: US-01 (User model), US-02 (JWT), US-03 (Resume model), US-04 (JobDescription model)
"""

import os
import sys
from datetime import datetime
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity

# Add paths for imports from previous US
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-01-User-Registration', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-03-Resume-Upload', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-04-JD-Upload', 'backend'))

try:
    from us01_user_model import User, db
    from us03_resume_model import Resume
    from us04_jd_model import JobDescription
    from us05_keyword_model import Keyword
    from us05_keyword_extractor import get_keyword_extractor
except ImportError as e:
    print(f"⚠️  Import warning: {e}")
    print("Some features may not work without previous US components")

# Create Blueprint for keyword processing routes
keyword_bp = Blueprint('keyword', __name__, url_prefix='/api')

@keyword_bp.route('/extract_keywords', methods=['POST'])
@jwt_required()
def extract_keywords():
    """
    Manual Keyword Extraction Endpoint
    
    POST /api/extract_keywords
    
    Manually trigger keyword extraction for a specific resume or job description.
    This is useful for reprocessing or testing.
    
    Required Headers:
        Authorization: Bearer <access_token>
    
    Request Body (JSON):
        {
            "resume_id": "uuid-here" OR "job_description_id": "uuid-here",
            "extraction_method": "spacy|nltk|hybrid (optional, default: hybrid)"
        }
    
    Returns:
        JSON response with extracted keywords
    """
    try:
        # Get current user from JWT token
        current_user_id = get_jwt_identity()
        
        # Verify user exists and is active
        user = User.query.get(current_user_id)
        if not user or not user.is_active:
            return jsonify({
                'success': False,
                'message': 'User not found or inactive'
            }), 401
        
        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400
        
        resume_id = data.get('resume_id')
        job_description_id = data.get('job_description_id')
        extraction_method = data.get('extraction_method', 'hybrid')
        
        # Validate input
        if not resume_id and not job_description_id:
            return jsonify({
                'success': False,
                'message': 'Either resume_id or job_description_id must be provided'
            }), 400
        
        if resume_id and job_description_id:
            return jsonify({
                'success': False,
                'message': 'Cannot specify both resume_id and job_description_id'
            }), 400
        
        # Get source document and text
        source_text = None
        source_type = None
        source_title = None
        
        if resume_id:
            resume = Resume.query.filter_by(id=resume_id, user_id=current_user_id).first()
            if not resume:
                return jsonify({
                    'success': False,
                    'message': 'Resume not found or does not belong to user'
                }), 404
            
            source_text = resume.extracted_text
            source_type = 'resume'
            source_title = resume.resume_title or resume.original_filename
            
            if not source_text:
                return jsonify({
                    'success': False,
                    'message': 'Resume text not available. Please ensure the resume was processed successfully.'
                }), 400
        
        elif job_description_id:
            jd = JobDescription.query.filter_by(id=job_description_id, user_id=current_user_id).first()
            if not jd:
                return jsonify({
                    'success': False,
                    'message': 'Job description not found or does not belong to user'
                }), 404
            
            source_text = jd.job_description_text
            source_type = 'job_description'
            source_title = jd.title
        
        # Extract keywords
        extractor = get_keyword_extractor(method=extraction_method)
        extracted_keywords = extractor.extract_keywords(source_text, source_type)
        
        if not extracted_keywords:
            return jsonify({
                'success': True,
                'message': 'No keywords extracted from the document',
                'keywords': [],
                'source': {
                    'type': source_type,
                    'title': source_title,
                    'id': resume_id or job_description_id
                }
            }), 200
        
        # Save keywords to database
        saved_keywords = []
        error_count = 0
        
        # Clear existing keywords for this document
        if resume_id:
            Keyword.query.filter_by(user_id=current_user_id, resume_id=resume_id).delete()
        else:
            Keyword.query.filter_by(user_id=current_user_id, job_description_id=job_description_id).delete()
        
        # Save new keywords
        for kw_data in extracted_keywords:
            keyword_obj, error = Keyword.create_keyword(
                user_id=current_user_id,
                resume_id=resume_id,
                job_description_id=job_description_id,
                keyword=kw_data['keyword'],
                keyword_type=kw_data['keyword_type'],
                frequency=kw_data.get('frequency', 1),
                confidence_score=kw_data['confidence_score'],
                context_snippet=kw_data.get('context_snippet'),
                position_in_text=kw_data.get('position_in_text'),
                extraction_method=kw_data['extraction_method']
            )
            
            if keyword_obj:
                saved_keywords.append(keyword_obj.to_dict())
            else:
                error_count += 1
                print(f"Error saving keyword: {error}")
        
        # Update processing status in source document
        if resume_id:
            resume.keywords_extracted = True
            resume.is_processed = True
            resume.updated_at = datetime.utcnow()
        else:
            jd.keywords_extracted = True
            jd.is_processed = True
            jd.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        # Success response
        return jsonify({
            'success': True,
            'message': f'Successfully extracted {len(saved_keywords)} keywords from {source_type}',
            'keywords': saved_keywords,
            'extraction_stats': {
                'total_extracted': len(extracted_keywords),
                'successfully_saved': len(saved_keywords),
                'errors': error_count,
                'extraction_method': extraction_method
            },
            'source': {
                'type': source_type,
                'title': source_title,
                'id': resume_id or job_description_id
            }
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'Error extracting keywords: {str(e)}'
        }), 500

@keyword_bp.route('/keywords', methods=['GET'])
@jwt_required()
def get_keywords():
    """
    Get Keywords List Endpoint
    
    GET /api/keywords
    
    Returns a list of keywords for the current user.
    
    Query Parameters:
        resume_id (str, optional): Filter by resume ID
        job_description_id (str, optional): Filter by job description ID
        keyword_type (str, optional): Filter by keyword type
        limit (int, optional): Maximum number of results (default: 100)
    
    Returns:
        JSON response with list of keywords
    """
    try:
        # Get current user from JWT token
        current_user_id = get_jwt_identity()
        
        # Get query parameters
        resume_id = request.args.get('resume_id')
        job_description_id = request.args.get('job_description_id')
        keyword_type = request.args.get('keyword_type')
        limit = request.args.get('limit', 100, type=int)
        
        # Validate limit
        if limit > 500:
            limit = 500
        
        # Get keywords
        if resume_id or job_description_id:
            keywords = Keyword.get_by_document(
                user_id=current_user_id,
                resume_id=resume_id,
                job_description_id=job_description_id,
                keyword_type=keyword_type
            )
        else:
            keywords = Keyword.get_by_user(current_user_id, limit=limit)
        
        # Convert to dictionary format
        keywords_list = [kw.to_dict() for kw in keywords[:limit]]
        
        return jsonify({
            'success': True,
            'message': f'Retrieved {len(keywords_list)} keywords',
            'keywords': keywords_list,
            'total_count': len(keywords_list),
            'filters': {
                'resume_id': resume_id,
                'job_description_id': job_description_id,
                'keyword_type': keyword_type,
                'limit': limit
            }
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error retrieving keywords: {str(e)}'
        }), 500

@keyword_bp.route('/keywords/unique', methods=['GET'])
@jwt_required()
def get_unique_keywords():
    """
    Get Unique Keywords Endpoint
    
    GET /api/keywords/unique
    
    Returns unique keywords (deduplicated) for a document or user.
    
    Query Parameters:
        resume_id (str, optional): Filter by resume ID
        job_description_id (str, optional): Filter by job description ID
    
    Returns:
        JSON response with unique keyword list
    """
    try:
        # Get current user from JWT token
        current_user_id = get_jwt_identity()
        
        # Get query parameters
        resume_id = request.args.get('resume_id')
        job_description_id = request.args.get('job_description_id')
        
        # Get unique keywords
        unique_keywords = Keyword.get_unique_keywords(
            user_id=current_user_id,
            resume_id=resume_id,
            job_description_id=job_description_id
        )
        
        return jsonify({
            'success': True,
            'message': f'Retrieved {len(unique_keywords)} unique keywords',
            'keywords': unique_keywords,
            'total_count': len(unique_keywords),
            'filters': {
                'resume_id': resume_id,
                'job_description_id': job_description_id
            }
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error retrieving unique keywords: {str(e)}'
        }), 500

@keyword_bp.route('/process_all', methods=['POST'])
@jwt_required()
def process_all_documents():
    """
    Process All Documents Endpoint
    
    POST /api/process_all
    
    Automatically extract keywords from all unprocessed resumes and job descriptions
    for the current user.
    
    Request Body (JSON):
        {
            "extraction_method": "spacy|nltk|hybrid (optional, default: hybrid)",
            "force_reprocess": false (optional, default: false)
        }
    
    Returns:
        JSON response with processing results
    """
    try:
        # Get current user from JWT token
        current_user_id = get_jwt_identity()
        
        # Get request data
        data = request.get_json() or {}
        extraction_method = data.get('extraction_method', 'hybrid')
        force_reprocess = data.get('force_reprocess', False)
        
        # Get documents to process
        resume_filter = {} if force_reprocess else {'keywords_extracted': False}
        jd_filter = {} if force_reprocess else {'keywords_extracted': False}
        
        resumes_to_process = Resume.query.filter_by(user_id=current_user_id, **resume_filter).all()
        jds_to_process = JobDescription.query.filter_by(user_id=current_user_id, **jd_filter).all()
        
        if not resumes_to_process and not jds_to_process:
            return jsonify({
                'success': True,
                'message': 'No documents need processing',
                'results': {
                    'resumes_processed': 0,
                    'jds_processed': 0,
                    'total_keywords_extracted': 0
                }
            }), 200
        
        # Initialize extractor
        extractor = get_keyword_extractor(method=extraction_method)
        
        # Process results
        results = {
            'resumes_processed': 0,
            'jds_processed': 0,
            'total_keywords_extracted': 0,
            'errors': []
        }
        
        # Process resumes
        for resume in resumes_to_process:
            if resume.extracted_text:
                try:
                    # Clear existing keywords if reprocessing
                    if force_reprocess:
                        Keyword.query.filter_by(user_id=current_user_id, resume_id=resume.id).delete()
                    
                    # Extract keywords
                    keywords = extractor.extract_keywords(resume.extracted_text, 'resume')
                    
                    # Save keywords
                    for kw_data in keywords:
                        Keyword.create_keyword(
                            user_id=current_user_id,
                            resume_id=resume.id,
                            keyword=kw_data['keyword'],
                            keyword_type=kw_data['keyword_type'],
                            frequency=kw_data.get('frequency', 1),
                            confidence_score=kw_data['confidence_score'],
                            context_snippet=kw_data.get('context_snippet'),
                            position_in_text=kw_data.get('position_in_text'),
                            extraction_method=kw_data['extraction_method']
                        )
                    
                    # Update resume status
                    resume.keywords_extracted = True
                    resume.is_processed = True
                    resume.updated_at = datetime.utcnow()
                    
                    results['resumes_processed'] += 1
                    results['total_keywords_extracted'] += len(keywords)
                    
                except Exception as e:
                    results['errors'].append(f"Resume {resume.id}: {str(e)}")
        
        # Process job descriptions
        for jd in jds_to_process:
            try:
                # Clear existing keywords if reprocessing
                if force_reprocess:
                    Keyword.query.filter_by(user_id=current_user_id, job_description_id=jd.id).delete()
                
                # Extract keywords
                keywords = extractor.extract_keywords(jd.job_description_text, 'job_description')
                
                # Save keywords
                for kw_data in keywords:
                    Keyword.create_keyword(
                        user_id=current_user_id,
                        job_description_id=jd.id,
                        keyword=kw_data['keyword'],
                        keyword_type=kw_data['keyword_type'],
                        frequency=kw_data.get('frequency', 1),
                        confidence_score=kw_data['confidence_score'],
                        context_snippet=kw_data.get('context_snippet'),
                        position_in_text=kw_data.get('position_in_text'),
                        extraction_method=kw_data['extraction_method']
                    )
                
                # Update JD status
                jd.keywords_extracted = True
                jd.is_processed = True
                jd.updated_at = datetime.utcnow()
                
                results['jds_processed'] += 1
                results['total_keywords_extracted'] += len(keywords)
                
            except Exception as e:
                results['errors'].append(f"Job Description {jd.id}: {str(e)}")
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'Processed {results["resumes_processed"]} resumes and {results["jds_processed"]} job descriptions',
            'results': results
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'Error processing documents: {str(e)}'
        }), 500

# Health check endpoint for the keyword processing service
@keyword_bp.route('/keyword_health', methods=['GET'])
def keyword_health_check():
    """
    Health check endpoint for the keyword processing service
    
    Returns:
        JSON response indicating service status
    """
    # Test extractor availability
    extractor_status = 'unknown'
    try:
        extractor = get_keyword_extractor()
        extractor_status = 'available'
    except Exception as e:
        extractor_status = f'error: {str(e)}'
    
    return jsonify({
        'success': True,
        'message': 'Keyword processing service is running',
        'service': 'US-05 Keyword Parsing',
        'extractor_status': extractor_status,
        'timestamp': datetime.utcnow().isoformat()
    }), 200
