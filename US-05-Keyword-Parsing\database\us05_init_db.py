"""
US-05: Keyword Parsing - Database Initialization
===============================================

This script initializes the database for US-05 Keyword Parsing feature.
It creates the keywords table and sets up the necessary relationships.

Tech Stack: Python, SQLAlchemy, PostgreSQL
Dependencies: US-01 (User model), US-03 (Resume model), US-04 (JobDescription model)

Usage:
    python us05_init_db.py

Environment Variables Required:
    - DATABASE_URL or individual DB connection parameters
    - See README_US05.md for complete setup instructions
"""

import os
import sys
from datetime import datetime

# Add paths for imports from previous US
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-01-User-Registration', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-03-Resume-Upload', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-04-JD-Upload', 'backend'))

try:
    from us01_user_model import db, User
    print("✅ Successfully imported User model from US-01")
except ImportError as e:
    print(f"❌ Error importing from US-01: {e}")
    print("Please ensure US-01 is properly set up before running US-05")
    sys.exit(1)

try:
    from us03_resume_model import Resume
    print("✅ Successfully imported Resume model from US-03")
except ImportError as e:
    print(f"❌ Error importing from US-03: {e}")
    print("Please ensure US-03 is properly set up before running US-05")
    sys.exit(1)

try:
    from us04_jd_model import JobDescription
    print("✅ Successfully imported JobDescription model from US-04")
except ImportError as e:
    print(f"❌ Error importing from US-04: {e}")
    print("Please ensure US-04 is properly set up before running US-05")
    sys.exit(1)

def create_keywords_table():
    """
    Create the keywords table using SQLAlchemy
    
    This function creates the table structure that matches
    the SQL schema defined in us05_schema.sql
    """
    try:
        # Import the Keyword model
        from us05_keyword_model import Keyword
        
        # Create all tables (this will create keywords table)
        db.create_all()
        
        print("✅ Keywords table created successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error creating keywords table: {e}")
        return False

def verify_database_setup():
    """
    Verify that the database setup is correct
    
    Returns:
        bool: True if setup is valid, False otherwise
    """
    try:
        # Check if users table exists (from US-01)
        users_count = User.query.count()
        print(f"✅ Users table verified - {users_count} users found")
        
        # Check if resumes table exists (from US-03)
        resumes_count = Resume.query.count()
        print(f"✅ Resumes table verified - {resumes_count} resumes found")
        
        # Check if job_descriptions table exists (from US-04)
        jd_count = JobDescription.query.count()
        print(f"✅ Job descriptions table verified - {jd_count} job descriptions found")
        
        # Check if keywords table exists
        from us05_keyword_model import Keyword
        keywords_count = Keyword.query.count()
        print(f"✅ Keywords table verified - {keywords_count} keywords found")
        
        return True
        
    except Exception as e:
        print(f"❌ Database verification failed: {e}")
        return False

def create_sample_data():
    """
    Create sample keyword data for testing
    
    This is optional and only runs if explicitly requested
    """
    try:
        from us05_keyword_model import Keyword
        
        # Check if we have any users to associate with
        sample_user = User.query.first()
        if not sample_user:
            print("⚠️  No users found. Please register a user first using US-01")
            return False
        
        # Check if we have resumes or job descriptions
        sample_resume = Resume.query.filter_by(user_id=sample_user.id).first()
        sample_jd = JobDescription.query.filter_by(user_id=sample_user.id).first()
        
        if not sample_resume and not sample_jd:
            print("⚠️  No resumes or job descriptions found. Please upload content using US-03/US-04")
            return False
        
        # Check if sample data already exists
        existing_keywords = Keyword.query.filter_by(user_id=sample_user.id).first()
        if existing_keywords:
            print("ℹ️  Sample keyword data already exists")
            return True
        
        # Create sample keywords for resume
        if sample_resume:
            resume_keywords = [
                {'keyword': 'Python', 'keyword_type': 'technology', 'frequency': 3, 'confidence_score': 0.95},
                {'keyword': 'Flask', 'keyword_type': 'framework', 'frequency': 2, 'confidence_score': 0.90},
                {'keyword': 'PostgreSQL', 'keyword_type': 'technology', 'frequency': 1, 'confidence_score': 0.85},
                {'keyword': 'Software Engineer', 'keyword_type': 'experience', 'frequency': 1, 'confidence_score': 0.92},
                {'keyword': 'Bachelor Degree', 'keyword_type': 'education', 'frequency': 1, 'confidence_score': 0.88}
            ]
            
            for kw_data in resume_keywords:
                keyword = Keyword(
                    user_id=sample_user.id,
                    resume_id=sample_resume.id,
                    **kw_data
                )
                db.session.add(keyword)
            
            print(f"✅ Created {len(resume_keywords)} sample keywords for resume")
        
        # Create sample keywords for job description
        if sample_jd:
            jd_keywords = [
                {'keyword': 'Python', 'keyword_type': 'technology', 'frequency': 5, 'confidence_score': 0.98},
                {'keyword': 'Flask', 'keyword_type': 'framework', 'frequency': 3, 'confidence_score': 0.95},
                {'keyword': 'React', 'keyword_type': 'framework', 'frequency': 2, 'confidence_score': 0.90},
                {'keyword': 'Senior Developer', 'keyword_type': 'experience', 'frequency': 2, 'confidence_score': 0.93},
                {'keyword': 'Computer Science', 'keyword_type': 'education', 'frequency': 1, 'confidence_score': 0.89}
            ]
            
            for kw_data in jd_keywords:
                keyword = Keyword(
                    user_id=sample_user.id,
                    job_description_id=sample_jd.id,
                    **kw_data
                )
                db.session.add(keyword)
            
            print(f"✅ Created {len(jd_keywords)} sample keywords for job description")
        
        db.session.commit()
        print("✅ Sample keyword data created successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating sample data: {e}")
        db.session.rollback()
        return False

def test_nlp_dependencies():
    """
    Test if NLP dependencies (spaCy/NLTK) are available
    
    Returns:
        bool: True if dependencies are available, False otherwise
    """
    print("\n🔍 Testing NLP dependencies...")
    
    # Test spaCy
    try:
        import spacy
        print("✅ spaCy is installed")
        
        # Try to load English model
        try:
            nlp = spacy.load("en_core_web_sm")
            print("✅ spaCy English model (en_core_web_sm) is available")
        except OSError:
            print("⚠️  spaCy English model not found. Install with: python -m spacy download en_core_web_sm")
            
    except ImportError:
        print("⚠️  spaCy not installed. Install with: pip install spacy")
    
    # Test NLTK
    try:
        import nltk
        print("✅ NLTK is installed")
        
        # Check for required NLTK data
        try:
            nltk.data.find('tokenizers/punkt')
            print("✅ NLTK punkt tokenizer is available")
        except LookupError:
            print("⚠️  NLTK punkt tokenizer not found. Download with: nltk.download('punkt')")
            
        try:
            nltk.data.find('corpora/stopwords')
            print("✅ NLTK stopwords are available")
        except LookupError:
            print("⚠️  NLTK stopwords not found. Download with: nltk.download('stopwords')")
            
    except ImportError:
        print("⚠️  NLTK not installed. Install with: pip install nltk")
    
    return True

def main():
    """
    Main function to initialize the database for US-05
    """
    print("🚀 Initializing US-05: Keyword Parsing Database")
    print("=" * 60)
    
    # Step 1: Test NLP dependencies
    print("\n📦 Step 1: Testing NLP dependencies...")
    test_nlp_dependencies()
    
    # Step 2: Create the keywords table
    print("\n📋 Step 2: Creating keywords table...")
    if not create_keywords_table():
        print("❌ Failed to create keywords table")
        return False
    
    # Step 3: Verify database setup
    print("\n🔍 Step 3: Verifying database setup...")
    if not verify_database_setup():
        print("❌ Database verification failed")
        return False
    
    # Step 4: Ask about sample data
    print("\n📊 Step 4: Sample data creation...")
    create_sample = input("Do you want to create sample keyword data? (y/N): ").lower().strip()
    
    if create_sample in ['y', 'yes']:
        if create_sample_data():
            print("✅ Sample data created successfully")
        else:
            print("⚠️  Sample data creation failed, but database setup is complete")
    else:
        print("ℹ️  Skipping sample data creation")
    
    print("\n🎉 US-05 Database initialization completed successfully!")
    print("\nNext steps:")
    print("1. Install NLP dependencies if not already installed:")
    print("   pip install spacy nltk")
    print("   python -m spacy download en_core_web_sm")
    print("   python -c \"import nltk; nltk.download('punkt'); nltk.download('stopwords')\"")
    print("2. Start the Flask application: python us05_app.py")
    print("3. Test keyword extraction: POST /api/extract_keywords")
    print("4. Check the processing status in US-03 and US-04")
    
    return True

if __name__ == "__main__":
    # Set up the application context for database operations
    try:
        # Import the Flask app to get the application context
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))
        from us05_app import create_app
        
        app = create_app()
        with app.app_context():
            success = main()
            if not success:
                sys.exit(1)
                
    except ImportError as e:
        print(f"❌ Error importing Flask app: {e}")
        print("Please ensure the backend components are created first")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)
