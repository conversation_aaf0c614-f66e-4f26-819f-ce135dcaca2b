-- US-05: Keyword Parsing - Database Schema
-- =========================================
-- 
-- This file contains the PostgreSQL database schema for US-05 Keyword Parsing
-- It defines the keywords table structure and relationships to resumes and job_descriptions
-- 
-- Tech Stack: PostgreSQL 13+
-- Dependencies: US-01 (users table), US-03 (resumes table), US-04 (job_descriptions table)

-- Note: This assumes the database and UUID extension are already created in US-01
-- If running independently, uncomment the following:
-- CREATE DATABASE dr_resume_db;
-- \c dr_resume_db;
-- CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create keywords table for US-05 Keyword Parsing
CREATE TABLE IF NOT EXISTS keywords (
    -- Primary key using UUID for better security
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    
    -- Foreign key to users table (from US-01)
    user_id VARCHAR(36) NOT NULL,
    
    -- Source document references (one of these will be set)
    resume_id VARCHAR(36),                               -- Reference to resumes table (US-03)
    job_description_id VARCHAR(36),                      -- Reference to job_descriptions table (US-04)
    
    -- Keyword information
    keyword VARCHAR(100) NOT NULL,                       -- The extracted keyword
    keyword_type VARCHAR(50) DEFAULT 'general',          -- skill, technology, experience, education, etc.
    frequency INTEGER DEFAULT 1,                         -- How many times it appears
    confidence_score DECIMAL(3,2) DEFAULT 0.80,          -- NLP confidence (0.00-1.00)
    
    -- Context information
    context_snippet TEXT,                                -- Surrounding text for context
    position_in_text INTEGER,                            -- Character position in original text
    
    -- Processing metadata
    extraction_method VARCHAR(50) DEFAULT 'spacy',       -- spacy, nltk, manual
    extraction_version VARCHAR(20) DEFAULT '1.0',        -- Version of extraction algorithm
    is_verified BOOLEAN DEFAULT FALSE,                   -- Manual verification flag
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    
    -- Constraints
    CONSTRAINT fk_keywords_user_id 
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_keywords_resume_id 
        FOREIGN KEY (resume_id) REFERENCES resumes(id) ON DELETE CASCADE,
    CONSTRAINT fk_keywords_job_description_id 
        FOREIGN KEY (job_description_id) REFERENCES job_descriptions(id) ON DELETE CASCADE,
    CONSTRAINT chk_keywords_source 
        CHECK ((resume_id IS NOT NULL AND job_description_id IS NULL) OR 
               (resume_id IS NULL AND job_description_id IS NOT NULL)),
    CONSTRAINT chk_keyword_type 
        CHECK (keyword_type IN ('skill', 'technology', 'experience', 'education', 'certification', 'tool', 'language', 'framework', 'general')),
    CONSTRAINT chk_confidence_score 
        CHECK (confidence_score >= 0.00 AND confidence_score <= 1.00),
    CONSTRAINT chk_frequency 
        CHECK (frequency >= 1)
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_keywords_user_id ON keywords(user_id);
CREATE INDEX IF NOT EXISTS idx_keywords_resume_id ON keywords(resume_id);
CREATE INDEX IF NOT EXISTS idx_keywords_job_description_id ON keywords(job_description_id);
CREATE INDEX IF NOT EXISTS idx_keywords_keyword ON keywords(keyword);
CREATE INDEX IF NOT EXISTS idx_keywords_keyword_type ON keywords(keyword_type);
CREATE INDEX IF NOT EXISTS idx_keywords_created_at ON keywords(created_at);

-- Create composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_keywords_user_resume ON keywords(user_id, resume_id);
CREATE INDEX IF NOT EXISTS idx_keywords_user_jd ON keywords(user_id, job_description_id);
CREATE INDEX IF NOT EXISTS idx_keywords_keyword_type_user ON keywords(keyword_type, user_id);

-- Create trigger to automatically update updated_at timestamp
-- (Reuse the function created in US-01)
CREATE TRIGGER trigger_keywords_updated_at
    BEFORE UPDATE ON keywords
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create a view for keyword summary by document
CREATE OR REPLACE VIEW keyword_summary AS
SELECT 
    k.user_id,
    k.resume_id,
    k.job_description_id,
    COUNT(*) as total_keywords,
    COUNT(DISTINCT k.keyword) as unique_keywords,
    COUNT(DISTINCT k.keyword_type) as keyword_types,
    AVG(k.confidence_score) as avg_confidence,
    MAX(k.created_at) as last_extraction,
    CASE 
        WHEN k.resume_id IS NOT NULL THEN 'resume'
        WHEN k.job_description_id IS NOT NULL THEN 'job_description'
        ELSE 'unknown'
    END as source_type
FROM keywords k
GROUP BY k.user_id, k.resume_id, k.job_description_id;

-- Create a view for top keywords by type
CREATE OR REPLACE VIEW top_keywords_by_type AS
SELECT 
    keyword_type,
    keyword,
    COUNT(*) as usage_count,
    AVG(confidence_score) as avg_confidence,
    COUNT(DISTINCT user_id) as user_count
FROM keywords
GROUP BY keyword_type, keyword
ORDER BY keyword_type, usage_count DESC;

-- Create a function to get keywords for a specific document
CREATE OR REPLACE FUNCTION get_document_keywords(
    p_user_id VARCHAR(36),
    p_resume_id VARCHAR(36) DEFAULT NULL,
    p_job_description_id VARCHAR(36) DEFAULT NULL,
    p_keyword_type VARCHAR(50) DEFAULT NULL
)
RETURNS TABLE (
    keyword VARCHAR(100),
    keyword_type VARCHAR(50),
    frequency INTEGER,
    confidence_score DECIMAL(3,2),
    context_snippet TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        k.keyword,
        k.keyword_type,
        k.frequency,
        k.confidence_score,
        k.context_snippet
    FROM keywords k
    WHERE k.user_id = p_user_id
        AND (p_resume_id IS NULL OR k.resume_id = p_resume_id)
        AND (p_job_description_id IS NULL OR k.job_description_id = p_job_description_id)
        AND (p_keyword_type IS NULL OR k.keyword_type = p_keyword_type)
    ORDER BY k.confidence_score DESC, k.frequency DESC;
END;
$$ LANGUAGE plpgsql;

-- Create a function to update processing status in source tables
CREATE OR REPLACE FUNCTION update_source_keyword_status(
    p_resume_id VARCHAR(36) DEFAULT NULL,
    p_job_description_id VARCHAR(36) DEFAULT NULL,
    p_status BOOLEAN DEFAULT TRUE
)
RETURNS BOOLEAN AS $$
BEGIN
    IF p_resume_id IS NOT NULL THEN
        UPDATE resumes 
        SET keywords_extracted = p_status,
            is_processed = p_status,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = p_resume_id;
        RETURN FOUND;
    END IF;
    
    IF p_job_description_id IS NOT NULL THEN
        UPDATE job_descriptions 
        SET keywords_extracted = p_status,
            is_processed = p_status,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = p_job_description_id;
        RETURN FOUND;
    END IF;
    
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- Insert sample data for testing (optional)
-- Uncomment the following lines if you want sample data

/*
-- First, get sample IDs from existing data
DO $$
DECLARE
    sample_user_id VARCHAR(36);
    sample_resume_id VARCHAR(36);
    sample_jd_id VARCHAR(36);
BEGIN
    -- Get sample user
    SELECT id INTO sample_user_id FROM users WHERE email = '<EMAIL>' LIMIT 1;
    
    IF sample_user_id IS NOT NULL THEN
        -- Get sample resume
        SELECT id INTO sample_resume_id FROM resumes WHERE user_id = sample_user_id LIMIT 1;
        
        -- Get sample job description
        SELECT id INTO sample_jd_id FROM job_descriptions WHERE user_id = sample_user_id LIMIT 1;
        
        -- Insert sample keywords for resume
        IF sample_resume_id IS NOT NULL THEN
            INSERT INTO keywords (user_id, resume_id, keyword, keyword_type, frequency, confidence_score) VALUES
            (sample_user_id, sample_resume_id, 'Python', 'technology', 3, 0.95),
            (sample_user_id, sample_resume_id, 'Flask', 'framework', 2, 0.90),
            (sample_user_id, sample_resume_id, 'PostgreSQL', 'technology', 1, 0.85),
            (sample_user_id, sample_resume_id, 'Software Engineer', 'experience', 1, 0.92),
            (sample_user_id, sample_resume_id, 'Bachelor Degree', 'education', 1, 0.88);
        END IF;
        
        -- Insert sample keywords for job description
        IF sample_jd_id IS NOT NULL THEN
            INSERT INTO keywords (user_id, job_description_id, keyword, keyword_type, frequency, confidence_score) VALUES
            (sample_user_id, sample_jd_id, 'Python', 'technology', 5, 0.98),
            (sample_user_id, sample_jd_id, 'Flask', 'framework', 3, 0.95),
            (sample_user_id, sample_jd_id, 'React', 'framework', 2, 0.90),
            (sample_user_id, sample_jd_id, 'Senior Developer', 'experience', 2, 0.93),
            (sample_user_id, sample_jd_id, 'Computer Science', 'education', 1, 0.89);
        END IF;
    END IF;
END $$;
*/

-- Verify table creation
SELECT 
    table_name, 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'keywords' 
ORDER BY ordinal_position;

-- Display table structure
\d keywords;

-- Show indexes
\di keywords*;

-- Show foreign key constraints
SELECT
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_name='keywords';

-- Show view definitions
\d+ keyword_summary;
\d+ top_keywords_by_type;

-- Test queries to verify relationships work
-- SELECT COUNT(*) as total_keywords FROM keywords;
-- SELECT keyword_type, COUNT(*) as count FROM keywords GROUP BY keyword_type;
-- SELECT * FROM keyword_summary LIMIT 5;
