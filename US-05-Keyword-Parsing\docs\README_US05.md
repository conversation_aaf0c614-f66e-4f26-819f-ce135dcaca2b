# US-05: Keyword Parsing - Complete Guide

## 📋 Overview

**US-05: Keyword Parsing** is the fifth user story in the Dr. Resume - AI Resume Scanner application. This feature automatically extracts keywords from resumes and job descriptions using advanced Natural Language Processing (NLP) techniques including spaCy and NLTK. It builds upon the text processing capabilities from US-03 and US-04.

### 🎯 What This US Accomplishes

- ✅ **Advanced NLP Processing**: spaCy and NLTK integration for keyword extraction
- ✅ **Automatic Processing**: Keywords extracted immediately after document upload
- ✅ **Multiple Extraction Methods**: spaCy, NLTK, hybrid, and pattern-matching approaches
- ✅ **Keyword Classification**: Categorizes keywords by type (skill, technology, experience, etc.)
- ✅ **Confidence Scoring**: Assigns confidence scores to extracted keywords
- ✅ **Context Preservation**: Stores surrounding text for keyword context
- ✅ **REST API**: Full CRUD operations for keyword management
- ✅ **JWT Authentication**: Protected endpoints using tokens from US-02
- ✅ **Database Integration**: Proper relationships with resumes and job descriptions
- ✅ **Comprehensive Testing**: Unit and integration tests for all NLP components

## 🏗️ Architecture Overview

```
US-05-Keyword-Parsing/
├── backend/                          # Flask API with NLP processing
│   ├── us05_keyword_model.py        # Keyword database model
│   ├── us05_keyword_extractor.py    # NLP extraction logic (spaCy/NLTK)
│   ├── us05_processing_routes.py    # Keyword processing API routes
│   ├── us05_auto_processor.py       # Automatic processing integration
│   ├── us05_app.py                  # Main Flask application
│   └── requirements.txt             # Python dependencies (includes spaCy/NLTK)
├── database/                        # Database schema and setup
│   ├── us05_schema.sql              # PostgreSQL schema for keywords table
│   └── us05_init_db.py              # Database initialization script
├── tests/                           # Comprehensive test suite
│   ├── test_us05_keyword_parsing.py # Main test file
│   └── conftest.py                  # Test configuration and fixtures
└── docs/                            # Documentation
    └── README_US05.md               # This file
```

## 🚀 Quick Start Guide

### Prerequisites

1. **Completed US-01 through US-04**: User registration, authentication, resume upload, and JD upload
2. **Python 3.9+** installed
3. **PostgreSQL 13+** running with dr_resume_db database
4. **spaCy and NLTK** for NLP processing

### Step 1: Install NLP Dependencies

1. **Install spaCy and Download Model**:
   ```bash
   pip install spacy
   python -m spacy download en_core_web_sm
   ```

2. **Install NLTK and Download Data**:
   ```bash
   pip install nltk
   python -c "import nltk; nltk.download('punkt'); nltk.download('stopwords'); nltk.download('averaged_perceptron_tagger'); nltk.download('maxent_ne_chunker'); nltk.download('words')"
   ```

### Step 2: Database Setup

1. **Run US-05 Database Schema**:
   ```bash
   cd US-05-Keyword-Parsing/database
   psql -U dr_resume_user -d dr_resume_db -f us05_schema.sql
   ```

2. **Initialize Database**:
   ```bash
   cd US-05-Keyword-Parsing/backend
   python us05_init_db.py
   ```

### Step 3: Backend Setup

1. **Navigate to Backend Directory**:
   ```bash
   cd US-05-Keyword-Parsing/backend
   ```

2. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure Environment Variables**:
   Create `.env` file in backend directory:
   ```env
   # Database Configuration
   DATABASE_URL=postgresql://dr_resume_user:your_secure_password@localhost/dr_resume_db
   
   # JWT Configuration (same as US-02)
   JWT_SECRET_KEY=your-jwt-secret-key-change-in-production
   SECRET_KEY=your-flask-secret-key-change-in-production
   
   # Application Configuration
   FLASK_ENV=development
   FLASK_DEBUG=True
   ```

4. **Start the Application**:
   ```bash
   python us05_app.py
   ```

   The application will start on `http://localhost:5000`

### Step 4: Test Keyword Extraction

1. **Upload Documents**: Use US-03 and US-04 to upload resumes and job descriptions
2. **Automatic Processing**: Keywords are extracted automatically after upload
3. **Manual Processing**: Use the API endpoints to trigger manual extraction
4. **View Results**: Check extracted keywords via API or database

## 🔧 Technical Implementation

### Database Schema

The `keywords` table stores all extracted keyword data:

```sql
CREATE TABLE keywords (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    user_id VARCHAR(36) NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    resume_id VARCHAR(36) REFERENCES resumes(id) ON DELETE CASCADE,
    job_description_id VARCHAR(36) REFERENCES job_descriptions(id) ON DELETE CASCADE,
    keyword VARCHAR(100) NOT NULL,
    keyword_type VARCHAR(50) DEFAULT 'general',
    frequency INTEGER DEFAULT 1,
    confidence_score DECIMAL(3,2) DEFAULT 0.80,
    context_snippet TEXT,
    position_in_text INTEGER,
    extraction_method VARCHAR(50) DEFAULT 'spacy',
    extraction_version VARCHAR(20) DEFAULT '1.0',
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### NLP Processing Pipeline

**1. Text Preprocessing**:
- Normalize whitespace and special characters
- Handle programming language variations (C++ → cpp, Node.js → nodejs)
- Clean and prepare text for NLP processing

**2. Keyword Extraction Methods**:

**spaCy Method**:
- Named Entity Recognition (NER) for organizations, products, skills
- Noun phrase extraction for technical terms
- Part-of-speech tagging for relevant keywords

**NLTK Method**:
- Tokenization and POS tagging
- Named entity chunking
- Noun and adjective extraction

**Pattern Matching**:
- Regex patterns for technical skills (Python, Java, React, etc.)
- Experience level patterns (senior, junior, 5+ years)
- Education patterns (Bachelor, Master, PhD)
- Certification patterns (AWS Certified, PMP, etc.)

**Hybrid Method**:
- Combines spaCy, NLTK, and pattern matching
- Uses best results from each method
- Provides highest accuracy and coverage

**3. Keyword Classification**:
- **Technology**: Programming languages, databases (Python, PostgreSQL)
- **Framework**: Web frameworks, libraries (Flask, React, Django)
- **Skill**: General skills and competencies
- **Experience**: Experience levels and roles (Senior Developer, 5 years)
- **Education**: Degrees and educational background
- **Certification**: Professional certifications
- **Tool**: Development tools and software
- **Language**: Programming and spoken languages

### API Endpoints

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| POST | `/api/extract_keywords` | Manual keyword extraction | Required |
| GET | `/api/keywords` | List user's keywords | Required |
| GET | `/api/keywords/unique` | Get unique keywords | Required |
| POST | `/api/process_all` | Process all unprocessed documents | Required |
| GET | `/api/keyword_health` | Health check for keyword service | None |

### Request/Response Examples

**Extract Keywords (POST /api/extract_keywords)**:
```json
{
    "resume_id": "uuid-here",
    "extraction_method": "hybrid"
}
```

**Response**:
```json
{
    "success": true,
    "message": "Successfully extracted 15 keywords from resume",
    "keywords": [
        {
            "id": "uuid-here",
            "keyword": "python",
            "keyword_type": "technology",
            "frequency": 3,
            "confidence_score": 0.95,
            "context_snippet": "Python developer with 5 years experience",
            "extraction_method": "spacy_ner"
        }
    ],
    "extraction_stats": {
        "total_extracted": 15,
        "successfully_saved": 15,
        "extraction_method": "hybrid"
    }
}
```

## 🧠 NLP Technology Deep Dive

### spaCy Integration

**Why spaCy?**
- Industrial-strength NLP library
- Pre-trained models for English
- Excellent named entity recognition
- Fast and memory-efficient

**spaCy Features Used**:
- `en_core_web_sm` model for English processing
- Named Entity Recognition (NER) for skills and technologies
- Noun phrase extraction for technical terms
- Part-of-speech tagging for keyword filtering

**Example spaCy Processing**:
```python
import spacy

nlp = spacy.load("en_core_web_sm")
doc = nlp("Python developer with Flask experience")

# Extract entities
for ent in doc.ents:
    if ent.label_ in ['ORG', 'PRODUCT', 'LANGUAGE']:
        print(f"Entity: {ent.text}, Label: {ent.label_}")

# Extract noun phrases
for chunk in doc.noun_chunks:
    print(f"Noun phrase: {chunk.text}")
```

### NLTK Integration

**Why NLTK?**
- Comprehensive natural language toolkit
- Extensive corpus and linguistic resources
- Excellent for educational and research purposes
- Strong tokenization and POS tagging

**NLTK Features Used**:
- Punkt tokenizer for sentence and word tokenization
- POS tagger for part-of-speech identification
- Named entity chunker for entity recognition
- Stopwords corpus for filtering common words

**Example NLTK Processing**:
```python
import nltk
from nltk.tokenize import word_tokenize
from nltk.tag import pos_tag
from nltk.chunk import ne_chunk

text = "Python developer with Flask experience"
tokens = word_tokenize(text)
pos_tags = pos_tag(tokens)
entities = ne_chunk(pos_tags)
```

### Pattern Matching

**Technical Skills Patterns**:
```python
tech_patterns = {
    'programming_languages': [
        r'\b(?:python|java|javascript|c\+\+|c#|php|ruby|go|rust)\b'
    ],
    'frameworks': [
        r'\b(?:react|angular|vue|django|flask|spring|express)\b'
    ],
    'databases': [
        r'\b(?:mysql|postgresql|mongodb|redis|elasticsearch)\b'
    ]
}
```

**Experience Patterns**:
```python
experience_patterns = [
    r'\b(?:senior|lead|principal|staff|architect|manager)\b',
    r'\b(?:\d+\+?\s+years?|years?\s+of\s+experience)\b'
]
```

## 🧪 Testing

### Running Tests

1. **Install Test Dependencies**:
   ```bash
   pip install pytest pytest-flask
   ```

2. **Run All Tests**:
   ```bash
   cd US-05-Keyword-Parsing/tests
   pytest test_us05_keyword_parsing.py -v
   ```

3. **Run Specific Test Categories**:
   ```bash
   # Unit tests only
   pytest -m unit
   
   # Integration tests only
   pytest -m integration
   
   # NLP tests only
   pytest -k "test_extract"
   ```

### Test Coverage

The test suite covers:
- ✅ Keyword model creation and validation
- ✅ NLP extraction methods (spaCy, NLTK, pattern matching)
- ✅ Text preprocessing and normalization
- ✅ Keyword classification and confidence scoring
- ✅ API endpoint functionality
- ✅ Authentication and authorization
- ✅ Automatic processing integration
- ✅ Error handling and edge cases

## 📚 Learning Guide for Beginners

### Understanding Natural Language Processing

**What is NLP?**
Natural Language Processing is a field of AI that helps computers understand, interpret, and generate human language. In US-05, we use NLP to:
- Extract meaningful keywords from text
- Classify keywords by type
- Assign confidence scores
- Understand context and relationships

**Key NLP Concepts**:

1. **Tokenization**: Breaking text into individual words or tokens
2. **Part-of-Speech Tagging**: Identifying grammatical roles (noun, verb, adjective)
3. **Named Entity Recognition**: Identifying specific entities (people, organizations, technologies)
4. **Stemming/Lemmatization**: Reducing words to their root forms
5. **Stop Words**: Common words that are usually filtered out (the, and, is)

### Understanding the Processing Flow

1. **Document Upload** (US-03/US-04):
   - User uploads resume or job description
   - Text is extracted and stored in database

2. **Automatic Trigger** (US-05):
   - After successful upload, keyword extraction is triggered
   - `auto_process_resume()` or `auto_process_job_description()` is called

3. **Text Preprocessing**:
   - Clean and normalize the text
   - Handle special cases (C++, Node.js, etc.)
   - Prepare for NLP processing

4. **Keyword Extraction**:
   - Apply selected extraction method (spaCy, NLTK, hybrid, pattern)
   - Extract potential keywords with confidence scores
   - Classify keywords by type

5. **Post-processing**:
   - Remove duplicates and very short keywords
   - Calculate frequency of occurrence
   - Rank by confidence and frequency

6. **Database Storage**:
   - Save keywords to database with metadata
   - Update document processing status
   - Link keywords to source document

### Key Files Explained

**Backend Files**:

1. **`us05_keyword_model.py`**: 
   - Defines the Keyword database model
   - Handles keyword creation, validation, and relationships
   - Provides methods for querying and managing keywords

2. **`us05_keyword_extractor.py`**:
   - Contains the main NLP processing logic
   - Implements spaCy, NLTK, and pattern matching methods
   - Handles text preprocessing and keyword classification

3. **`us05_processing_routes.py`**:
   - Defines API endpoints for keyword processing
   - Handles manual extraction requests
   - Provides keyword retrieval and management

4. **`us05_auto_processor.py`**:
   - Integrates with US-03 and US-04 for automatic processing
   - Processes documents immediately after upload
   - Manages batch processing operations

5. **`us05_app.py`**:
   - Main Flask application configuration
   - Integrates all components and blueprints
   - Handles application startup and health checks

**Database Files**:

1. **`us05_schema.sql`**:
   - Creates the keywords table structure
   - Defines relationships and constraints
   - Includes indexes for performance

2. **`us05_init_db.py`**:
   - Automates database setup and initialization
   - Tests NLP dependencies
   - Creates sample data for testing

### Integration with Other US Features

**Dependencies**:
- **US-01**: User model and database setup
- **US-02**: JWT authentication for API security
- **US-03**: Resume model and text extraction
- **US-04**: Job description model and text storage

**Prepares for**:
- **US-06**: Matching score calculation using extracted keywords
- **US-07**: Basic suggestions based on keyword analysis

**Data Flow**:
```
US-03 (Resume Upload) → US-05 (Keyword Extraction) → US-06 (Matching)
US-04 (JD Upload) → US-05 (Keyword Extraction) → US-06 (Matching)
```

## 🔄 Automatic Processing Integration

### How Automatic Processing Works

1. **Upload Trigger**: When a resume or JD is uploaded via US-03/US-04
2. **Text Availability Check**: Ensure extracted text is available
3. **Keyword Extraction**: Run NLP processing on the text
4. **Database Storage**: Save extracted keywords with metadata
5. **Status Update**: Mark document as processed

### Integration Points

**In US-03 Resume Upload**:
```python
# After successful resume upload and text extraction
from us05_auto_processor import auto_process_resume

success, message, count = auto_process_resume(resume.id, user.id)
if success:
    print(f"Extracted {count} keywords from resume")
```

**In US-04 Job Description Upload**:
```python
# After successful JD upload
from us05_auto_processor import auto_process_job_description

success, message, count = auto_process_job_description(jd.id, user.id)
if success:
    print(f"Extracted {count} keywords from job description")
```

## 🚨 Common Issues and Solutions

### Issue 1: spaCy Model Not Found
**Problem**: `OSError: [E050] Can't find model 'en_core_web_sm'`
**Solution**: 
```bash
python -m spacy download en_core_web_sm
```

### Issue 2: NLTK Data Missing
**Problem**: `LookupError: Resource punkt not found`
**Solution**:
```bash
python -c "import nltk; nltk.download('punkt'); nltk.download('stopwords')"
```

### Issue 3: No Keywords Extracted
**Problem**: Extraction returns empty results
**Solution**: 
- Check if text is available in the document
- Verify extraction method is working
- Try different extraction methods (hybrid, pattern)

### Issue 4: Low Confidence Scores
**Problem**: Keywords have very low confidence scores
**Solution**:
- Review and adjust confidence thresholds
- Improve text preprocessing
- Use hybrid method for better accuracy

## 🔐 Security Considerations

1. **Authentication**: All endpoints require valid JWT tokens
2. **Authorization**: Users can only access their own keywords
3. **Input Validation**: All text inputs are validated and sanitized
4. **SQL Injection**: Using SQLAlchemy ORM prevents SQL injection
5. **Rate Limiting**: Consider implementing rate limiting for extraction endpoints

## 🎯 Performance Optimization

### NLP Performance Tips

1. **Model Loading**: Load spaCy models once at startup
2. **Batch Processing**: Process multiple documents together
3. **Caching**: Cache extraction results for identical text
4. **Async Processing**: Use background tasks for large documents
5. **Memory Management**: Monitor memory usage with large texts

### Database Performance

1. **Indexes**: Proper indexes on keyword, user_id, and document IDs
2. **Bulk Operations**: Use bulk insert for multiple keywords
3. **Query Optimization**: Efficient queries for keyword retrieval
4. **Connection Pooling**: Use connection pooling for database access

## 🎓 Advanced Features

### Custom Keyword Types

Add new keyword types by updating the model:
```python
# In us05_keyword_model.py
valid_types = ['skill', 'technology', 'experience', 'education', 
               'certification', 'tool', 'language', 'framework', 
               'general', 'custom_type']  # Add your custom type
```

### Custom Extraction Patterns

Add industry-specific patterns:
```python
# In us05_keyword_extractor.py
custom_patterns = {
    'medical_terms': [
        r'\b(?:hipaa|fda|clinical\s+trial|medical\s+device)\b'
    ],
    'finance_terms': [
        r'\b(?:sox|basel|risk\s+management|derivatives)\b'
    ]
}
```

### Confidence Score Tuning

Adjust confidence scores based on extraction method:
```python
confidence_weights = {
    'spacy_ner': 0.95,      # High confidence for NER
    'pattern_matching': 0.90, # High confidence for exact patterns
    'nltk_pos': 0.70,       # Medium confidence for POS tagging
    'noun_chunks': 0.65     # Lower confidence for noun phrases
}
```

---

**🎉 Congratulations!** You've successfully implemented US-05: Keyword Parsing with advanced NLP capabilities. Your application can now automatically extract and classify keywords from resumes and job descriptions, setting the foundation for intelligent matching in US-06.
