"""
US-05: Keyword Parsing - Test Suite
==================================

This file contains comprehensive tests for US-05 Keyword Parsing functionality.
It tests the keyword model, extraction logic, and API endpoints.

Tech Stack: pytest, Flask-Testing, SQLAlchemy, spaCy/NLTK
Dependencies: US-01 (User model), US-02 (JWT authentication), US-03 (Resume model), US-04 (JobDescription model)
"""

import pytest
import json
import sys
import os
from datetime import datetime

# Add backend directories to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-01-User-Registration', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-02-Login-JWT-Token', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-03-Resume-Upload', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-04-JD-Upload', 'backend'))

from us05_app import create_app
from us01_user_model import db, User
from us03_resume_model import Resume
from us04_jd_model import JobDescription
from us05_keyword_model import Keyword
from us05_keyword_extractor import KeywordExtractor, get_keyword_extractor

class TestKeywordModel:
    """Test cases for Keyword model functionality"""
    
    @pytest.fixture
    def app(self):
        """Create test Flask application"""
        app = create_app()
        app.config['TESTING'] = True
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        app.config['JWT_SECRET_KEY'] = 'test-secret-key'
        
        with app.app_context():
            db.create_all()
            yield app
            db.drop_all()
    
    @pytest.fixture
    def client(self, app):
        """Create test client"""
        return app.test_client()
    
    @pytest.fixture
    def sample_user(self, app):
        """Create a sample user for testing"""
        with app.app_context():
            user = User(
                email='<EMAIL>',
                password='password123',
                first_name='Test',
                last_name='User'
            )
            db.session.add(user)
            db.session.commit()
            return user
    
    @pytest.fixture
    def sample_resume(self, app, sample_user):
        """Create a sample resume for testing"""
        with app.app_context():
            resume = Resume(
                user_id=sample_user.id,
                original_filename='test_resume.pdf',
                extracted_text='Python developer with 5 years experience in Flask and PostgreSQL'
            )
            db.session.add(resume)
            db.session.commit()
            return resume
    
    @pytest.fixture
    def sample_jd(self, app, sample_user):
        """Create a sample job description for testing"""
        with app.app_context():
            jd = JobDescription(
                user_id=sample_user.id,
                title='Senior Python Developer',
                job_description_text='Looking for a Senior Python Developer with Flask experience and PostgreSQL knowledge'
            )
            db.session.add(jd)
            db.session.commit()
            return jd
    
    def test_keyword_creation(self, app, sample_user, sample_resume):
        """Test keyword creation with valid data"""
        with app.app_context():
            keyword = Keyword(
                user_id=sample_user.id,
                resume_id=sample_resume.id,
                keyword='python',
                keyword_type='technology',
                frequency=3,
                confidence_score=0.95
            )
            
            db.session.add(keyword)
            db.session.commit()
            
            # Verify creation
            assert keyword.id is not None
            assert keyword.keyword == 'python'
            assert keyword.user_id == sample_user.id
            assert keyword.resume_id == sample_resume.id
            assert keyword.keyword_type == 'technology'
            assert keyword.frequency == 3
            assert float(keyword.confidence_score) == 0.95
    
    def test_keyword_to_dict(self, app, sample_user, sample_resume):
        """Test keyword to_dict method"""
        with app.app_context():
            keyword = Keyword(
                user_id=sample_user.id,
                resume_id=sample_resume.id,
                keyword='flask',
                keyword_type='framework',
                frequency=2,
                confidence_score=0.90
            )
            
            db.session.add(keyword)
            db.session.commit()
            
            keyword_dict = keyword.to_dict()
            
            assert keyword_dict['id'] == keyword.id
            assert keyword_dict['keyword'] == 'flask'
            assert keyword_dict['keyword_type'] == 'framework'
            assert keyword_dict['frequency'] == 2
            assert keyword_dict['confidence_score'] == 0.90
            assert keyword_dict['source_type'] == 'resume'
    
    def test_keyword_create_method(self, app, sample_user, sample_resume):
        """Test Keyword.create_keyword class method"""
        with app.app_context():
            keyword, error = Keyword.create_keyword(
                user_id=sample_user.id,
                resume_id=sample_resume.id,
                keyword='postgresql',
                keyword_type='technology',
                frequency=1,
                confidence_score=0.85
            )
            
            assert error is None
            assert keyword is not None
            assert keyword.keyword == 'postgresql'
            assert keyword.keyword_type == 'technology'
            assert keyword.frequency == 1
    
    def test_keyword_validation_errors(self, app, sample_user, sample_resume):
        """Test keyword validation with invalid data"""
        with app.app_context():
            # Test empty keyword
            keyword, error = Keyword.create_keyword(
                user_id=sample_user.id,
                resume_id=sample_resume.id,
                keyword='',
                keyword_type='technology'
            )
            assert keyword is None
            assert 'keyword text is required' in error.lower()
            
            # Test invalid keyword type
            keyword, error = Keyword.create_keyword(
                user_id=sample_user.id,
                resume_id=sample_resume.id,
                keyword='python',
                keyword_type='invalid-type'
            )
            assert keyword is None
            assert 'invalid keyword type' in error.lower()
    
    def test_get_by_document(self, app, sample_user, sample_resume):
        """Test getting keywords by document"""
        with app.app_context():
            # Create multiple keywords
            keywords_data = [
                {'keyword': 'python', 'keyword_type': 'technology'},
                {'keyword': 'flask', 'keyword_type': 'framework'},
                {'keyword': 'senior', 'keyword_type': 'experience'}
            ]
            
            for kw_data in keywords_data:
                Keyword.create_keyword(
                    user_id=sample_user.id,
                    resume_id=sample_resume.id,
                    **kw_data
                )
            
            # Test retrieval
            keywords = Keyword.get_by_document(sample_user.id, resume_id=sample_resume.id)
            assert len(keywords) == 3
            
            # Test with keyword type filter
            tech_keywords = Keyword.get_by_document(
                sample_user.id, 
                resume_id=sample_resume.id, 
                keyword_type='technology'
            )
            assert len(tech_keywords) == 1
            assert tech_keywords[0].keyword == 'python'

class TestKeywordExtractor:
    """Test cases for KeywordExtractor functionality"""
    
    def test_extractor_initialization(self):
        """Test keyword extractor initialization"""
        extractor = KeywordExtractor(method='pattern')  # Use pattern method to avoid NLP dependencies
        assert extractor.method == 'pattern'
        assert extractor.language == 'en'
    
    def test_text_preprocessing(self):
        """Test text preprocessing functionality"""
        extractor = KeywordExtractor(method='pattern')
        
        # Test basic preprocessing
        text = "  Python   developer with C++ and Node.js experience  "
        cleaned = extractor._preprocess_text(text)
        
        assert 'python' in cleaned.lower()
        assert 'cpp' in cleaned.lower()  # C++ should be normalized
        assert 'nodejs' in cleaned.lower()  # Node.js should be normalized
    
    def test_pattern_extraction(self):
        """Test pattern-based keyword extraction"""
        extractor = KeywordExtractor(method='pattern')
        
        text = "Python developer with 5 years experience in Flask and PostgreSQL. Bachelor's degree in Computer Science."
        keywords = extractor._extract_with_patterns(text, 'resume')
        
        # Should find technical keywords
        keyword_texts = [kw['keyword'] for kw in keywords]
        assert any('python' in kw.lower() for kw in keyword_texts)
        assert any('flask' in kw.lower() for kw in keyword_texts)
        assert any('postgresql' in kw.lower() for kw in keyword_texts)
    
    def test_keyword_classification(self):
        """Test keyword type classification"""
        extractor = KeywordExtractor(method='pattern')
        
        # Test technology classification
        assert extractor._classify_keyword_type('Python') == 'technology'
        assert extractor._classify_keyword_type('Flask') == 'framework'
        assert extractor._classify_keyword_type('PostgreSQL') == 'technology'
        assert extractor._classify_keyword_type('5 years experience') == 'experience'
        assert extractor._classify_keyword_type('Bachelor degree') == 'education'
    
    def test_extract_keywords_integration(self):
        """Test full keyword extraction integration"""
        extractor = KeywordExtractor(method='pattern')
        
        text = "Senior Python developer with Flask framework experience and PostgreSQL database skills"
        keywords = extractor.extract_keywords(text, 'resume')
        
        assert len(keywords) > 0
        
        # Check that keywords have required fields
        for keyword in keywords:
            assert 'keyword' in keyword
            assert 'keyword_type' in keyword
            assert 'confidence_score' in keyword
            assert 'extraction_method' in keyword
    
    def test_get_keyword_extractor_singleton(self):
        """Test singleton pattern for keyword extractor"""
        extractor1 = get_keyword_extractor()
        extractor2 = get_keyword_extractor()
        
        assert extractor1 is extractor2  # Should be the same instance

class TestKeywordAPI:
    """Test cases for Keyword API endpoints"""
    
    @pytest.fixture
    def app(self):
        """Create test Flask application"""
        app = create_app()
        app.config['TESTING'] = True
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        app.config['JWT_SECRET_KEY'] = 'test-secret-key'
        
        with app.app_context():
            db.create_all()
            yield app
            db.drop_all()
    
    @pytest.fixture
    def client(self, app):
        """Create test client"""
        return app.test_client()
    
    @pytest.fixture
    def auth_user(self, app, client):
        """Create and authenticate a user"""
        with app.app_context():
            # Register user
            user_data = {
                'email': '<EMAIL>',
                'password': 'password123',
                'first_name': 'Test',
                'last_name': 'User'
            }
            
            response = client.post('/api/register', 
                                 data=json.dumps(user_data),
                                 content_type='application/json')
            
            # Login user
            login_data = {
                'email': '<EMAIL>',
                'password': 'password123'
            }
            
            response = client.post('/api/login',
                                 data=json.dumps(login_data),
                                 content_type='application/json')
            
            data = json.loads(response.data)
            return data['tokens']['access_token']
    
    @pytest.fixture
    def sample_resume_with_text(self, app, auth_user):
        """Create a sample resume with extracted text"""
        with app.app_context():
            # Get user
            user = User.query.filter_by(email='<EMAIL>').first()
            
            resume = Resume(
                user_id=user.id,
                original_filename='test_resume.pdf',
                extracted_text='Python developer with 5 years experience in Flask and PostgreSQL. Bachelor degree in Computer Science.'
            )
            db.session.add(resume)
            db.session.commit()
            return resume.id
    
    def test_extract_keywords_success(self, client, auth_user, sample_resume_with_text):
        """Test successful keyword extraction"""
        extraction_data = {
            'resume_id': sample_resume_with_text,
            'extraction_method': 'pattern'  # Use pattern method for testing
        }
        
        response = client.post('/api/extract_keywords',
                             data=json.dumps(extraction_data),
                             content_type='application/json',
                             headers={'Authorization': f'Bearer {auth_user}'})
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] == True
        assert 'keywords' in data
        assert len(data['keywords']) > 0
    
    def test_extract_keywords_validation_errors(self, client, auth_user):
        """Test keyword extraction with validation errors"""
        # Test missing resume_id and job_description_id
        extraction_data = {
            'extraction_method': 'pattern'
        }
        
        response = client.post('/api/extract_keywords',
                             data=json.dumps(extraction_data),
                             content_type='application/json',
                             headers={'Authorization': f'Bearer {auth_user}'})
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['success'] == False
        assert 'must be provided' in data['message']
    
    def test_extract_keywords_unauthorized(self, client):
        """Test keyword extraction without authentication"""
        extraction_data = {
            'resume_id': 'test-id',
            'extraction_method': 'pattern'
        }
        
        response = client.post('/api/extract_keywords',
                             data=json.dumps(extraction_data),
                             content_type='application/json')
        
        assert response.status_code == 401
    
    def test_get_keywords(self, client, auth_user, sample_resume_with_text):
        """Test getting keywords list"""
        # First extract keywords
        extraction_data = {
            'resume_id': sample_resume_with_text,
            'extraction_method': 'pattern'
        }
        
        client.post('/api/extract_keywords',
                   data=json.dumps(extraction_data),
                   content_type='application/json',
                   headers={'Authorization': f'Bearer {auth_user}'})
        
        # Get keywords
        response = client.get('/api/keywords',
                            headers={'Authorization': f'Bearer {auth_user}'})
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] == True
        assert 'keywords' in data
        assert len(data['keywords']) > 0
    
    def test_get_unique_keywords(self, client, auth_user, sample_resume_with_text):
        """Test getting unique keywords"""
        # First extract keywords
        extraction_data = {
            'resume_id': sample_resume_with_text,
            'extraction_method': 'pattern'
        }
        
        client.post('/api/extract_keywords',
                   data=json.dumps(extraction_data),
                   content_type='application/json',
                   headers={'Authorization': f'Bearer {auth_user}'})
        
        # Get unique keywords
        response = client.get(f'/api/keywords/unique?resume_id={sample_resume_with_text}',
                            headers={'Authorization': f'Bearer {auth_user}'})
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] == True
        assert 'keywords' in data
        assert isinstance(data['keywords'], list)
    
    def test_keyword_health_check(self, client):
        """Test keyword health check endpoint"""
        response = client.get('/api/keyword_health')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] == True
        assert 'US-05' in data['service']

if __name__ == '__main__':
    pytest.main([__file__])
