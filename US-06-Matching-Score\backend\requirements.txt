# US-06: Matching Score - Python Dependencies
# ===========================================
# 
# This file contains all Python packages required for US-06 Matching Score feature
# Install with: pip install -r requirements.txt
# 
# Dependencies include packages from US-01 through US-05, plus new matching requirements

# Core Flask Framework (from previous US)
Flask==2.3.3
Werkzeug==2.3.7

# Database ORM and PostgreSQL (from previous US)
Flask-SQLAlchemy==3.0.5
SQLAlchemy==2.0.21
psycopg2-binary==2.9.7

# JWT Authentication (from US-02)
Flask-JWT-Extended==4.5.3
PyJWT==2.8.0

# Password Hashing (from US-01)
bcrypt==4.0.1

# CORS Support for Frontend Integration (from previous US)
Flask-CORS==4.0.0

# Environment Variables Management (from previous US)
python-dotenv==1.0.0

# Date and Time Utilities (from previous US)
python-dateutil==2.8.2

# JSON Handling and Validation (from previous US)
jsonschema==4.19.1

# File Processing (from US-03)
PyPDF2==3.0.1
python-docx==0.8.11

# Natural Language Processing Libraries (from US-05)
spacy==3.6.1
nltk==3.8.1
textblob==0.17.1
regex==2023.8.8

# NEW: Matching and Similarity Calculation Libraries for US-06
# ============================================================

# Scientific computing for similarity calculations
numpy==1.24.3
scipy==1.11.2

# Machine learning utilities for advanced similarity metrics
scikit-learn==1.3.0

# Data manipulation for matching analysis
pandas==2.0.3

# Statistical analysis for matching confidence
statsmodels==0.14.0

# Fuzzy string matching for keyword similarity
fuzzywuzzy==0.18.0
python-Levenshtein==0.21.1

# Text similarity and distance calculations
textdistance==4.6.0

# Advanced similarity metrics
jellyfish==0.11.2

# Performance optimization for large-scale matching
numba==0.58.1

# Caching for matching calculations
joblib==1.3.2

# Progress tracking for batch calculations
tqdm==4.66.1

# Mathematical utilities for similarity algorithms
sympy==1.12

# Graph-based similarity calculations (optional)
networkx==3.1

# Clustering for keyword grouping (optional)
scikit-learn==1.3.0

# Development and Testing Dependencies
pytest==7.4.2
pytest-flask==1.2.0
requests==2.31.0

# Production Server (optional)
gunicorn==21.2.0

# Logging and Monitoring
colorlog==6.7.0

# Security
cryptography==41.0.4

# Data Validation
marshmallow==3.20.1
email-validator==2.0.0

# Performance Monitoring (optional)
flask-limiter==3.5.0

# Configuration Management
configparser==6.0.0

# Memory profiling for matching operations (optional)
memory-profiler==0.61.0

# Visualization for matching results (optional)
matplotlib==3.7.2
seaborn==0.12.2

# Additional text processing utilities (from US-05)
unidecode==1.3.6
langdetect==1.0.9

# Word frequency analysis (from US-05)
wordcloud==1.9.2

# Named entity recognition enhancements (from US-05)
spacy-transformers==1.2.5

# Built-in Python modules (listed for clarity):
# collections - Specialized container datatypes (built-in)
# decimal - Decimal arithmetic (built-in)
# time - Time-related functions (built-in)
# typing - Type hints (built-in)
# re - Regular expressions (built-in)
# os - Operating system interface (built-in)
# sys - System-specific parameters (built-in)
# datetime - Date and time (built-in)
# json - JSON encoder/decoder (built-in)
# uuid - UUID generation (built-in)
# math - Mathematical functions (built-in)
# statistics - Statistical functions (built-in)
