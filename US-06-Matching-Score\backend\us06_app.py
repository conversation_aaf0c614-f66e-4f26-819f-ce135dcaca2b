"""
US-06: Matching Score - Main Flask Application
==============================================

This is the main Flask application for US-06 Matching Score feature.
It integrates with previous US features and provides matching score calculation functionality.

Tech Stack: Flask, Flask-JWT-Extended, SQLAlchemy, PostgreSQL
Dependencies: US-01 (User Registration), US-02 (JWT Authentication), US-03 (Resume Upload), 
              US-04 (JD Upload), US-05 (Keyword Parsing)

Usage:
    python us06_app.py

Environment Variables Required:
    - DATABASE_URL or individual DB connection parameters
    - JWT_SECRET_KEY
    - See README_US06.md for complete setup instructions
"""

import os
import sys
from datetime import datetime, timedelta
from flask import Flask, jsonify
from flask_jwt_extended import JWTManager
from flask_cors import CORS

# Add paths for imports from previous US
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-01-User-Registration', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-02-Login-JWT-Token', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-03-Resume-Upload', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-04-JD-Upload', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-05-Keyword-Parsing', 'backend'))

try:
    from us01_user_model import db, User
    from us02_auth_routes import auth_bp
    from us02_auth_routes import (
        check_if_token_revoked, jwt_additional_claims_loader,
        expired_token_callback, invalid_token_callback, missing_token_callback
    )
    from us03_resume_model import Resume
    from us04_jd_model import JobDescription
    from us05_keyword_model import Keyword
    print("✅ Successfully imported from US-01 through US-05")
except ImportError as e:
    print(f"⚠️  Import warning: {e}")
    print("Some features may not work without previous US components")
    db = None
    auth_bp = None

# Import US-06 components
try:
    from us06_matching_model import MatchingScore
    from us06_matching_routes import matching_bp
    from us06_matching_calculator import get_matching_calculator
    print("✅ Successfully imported US-06 components")
except ImportError as e:
    print(f"❌ Error importing US-06 components: {e}")
    sys.exit(1)

def create_app():
    """
    Create and configure the Flask application for US-06
    
    Returns:
        Flask: Configured Flask application
    """
    app = Flask(__name__)
    
    # Configuration
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production')
    
    # Database configuration
    app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get(
        'DATABASE_URL',
        'postgresql://username:password@localhost/dr_resume_db'
    )
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
    }
    
    # JWT Configuration (from US-02)
    app.config['JWT_SECRET_KEY'] = os.environ.get('JWT_SECRET_KEY', 'jwt-secret-string-change-in-production')
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=1)
    app.config['JWT_REFRESH_TOKEN_EXPIRES'] = timedelta(days=30)
    app.config['JWT_BLACKLIST_ENABLED'] = True
    app.config['JWT_BLACKLIST_TOKEN_CHECKS'] = ['access', 'refresh']
    
    # Initialize extensions
    if db:
        db.init_app(app)
    
    # Initialize JWT
    jwt = JWTManager(app)
    
    # JWT Configuration (from US-02)
    if 'check_if_token_revoked' in globals():
        jwt.token_in_blocklist_loader(check_if_token_revoked)
        jwt.additional_claims_loader(jwt_additional_claims_loader)
        jwt.expired_token_loader(expired_token_callback)
        jwt.invalid_token_loader(invalid_token_callback)
        jwt.unauthorized_loader(missing_token_callback)
    
    # Enable CORS for frontend integration
    CORS(app, origins=['http://localhost:3000', 'http://127.0.0.1:3000'])
    
    # Register blueprints
    if auth_bp:
        app.register_blueprint(auth_bp)
        print("✅ Registered authentication blueprint from US-02")
    
    app.register_blueprint(matching_bp)
    print("✅ Registered matching score blueprint")
    
    # Root endpoint
    @app.route('/')
    def home():
        """Home endpoint with API information"""
        return jsonify({
            'success': True,
            'message': 'Dr. Resume API - US-06: Matching Score',
            'version': '1.0.0',
            'features': {
                'us01_registration': 'active' if auth_bp else 'not_available',
                'us02_jwt_auth': 'active' if auth_bp else 'not_available',
                'us03_resume_upload': 'active' if Resume else 'not_available',
                'us04_jd_upload': 'active' if JobDescription else 'not_available',
                'us05_keyword_parsing': 'active' if Keyword else 'not_available',
                'us06_matching_score': 'active'
            },
            'endpoints': {
                'calculate_match': 'POST /api/calculate_match',
                'matching_scores': 'GET /api/matching_scores',
                'matching_score_details': 'GET /api/matching_scores/<id>',
                'best_matches': 'GET /api/best_matches',
                'recalculate_all': 'POST /api/recalculate_all',
                'matching_health': 'GET /api/matching_health'
            },
            'matching_capabilities': {
                'calculation_methods': ['jaccard', 'weighted', 'hybrid'],
                'similarity_algorithms': ['Jaccard similarity', 'Weighted similarity', 'Hybrid approach'],
                'supported_categories': ['skill', 'experience', 'education', 'technology', 'framework'],
                'progress_bar_display': True,
                'color_coded_indicators': True
            },
            'authentication': {
                'required': True,
                'type': 'JWT Bearer Token',
                'login_endpoint': '/api/login',
                'register_endpoint': '/api/register'
            },
            'timestamp': datetime.utcnow().isoformat()
        })
    
    # Health check endpoint
    @app.route('/health')
    def health_check():
        """Comprehensive health check for US-06"""
        
        # Check database connection
        db_status = 'unknown'
        user_count = 0
        resume_count = 0
        jd_count = 0
        keyword_count = 0
        matching_score_count = 0
        
        try:
            if db:
                # Test database connection
                db.session.execute('SELECT 1')
                db_status = 'connected'
                
                # Get counts
                user_count = User.query.count() if User else 0
                resume_count = Resume.query.count() if Resume else 0
                jd_count = JobDescription.query.count() if JobDescription else 0
                keyword_count = Keyword.query.count() if Keyword else 0
                matching_score_count = MatchingScore.query.count()
            else:
                db_status = 'not_configured'
        except Exception as e:
            db_status = f'error: {str(e)}'
        
        # Check matching calculator status
        calculator_status = 'unknown'
        calculator_info = {}
        
        try:
            calculator = get_matching_calculator()
            calculator_status = 'available'
            calculator_info = {
                'algorithm_version': calculator.algorithm_version,
                'keyword_weights_configured': len(calculator.keyword_weights) > 0
            }
        except Exception as e:
            calculator_status = f'error: {str(e)}'
        
        # Check processing readiness
        processed_resumes = Resume.query.filter_by(keywords_extracted=True).count() if Resume else 0
        processed_jds = JobDescription.query.filter_by(keywords_extracted=True).count() if JobDescription else 0
        
        return jsonify({
            'success': True,
            'message': 'Application is running',
            'status': {
                'app': 'running',
                'database': db_status,
                'jwt': 'configured',
                'matching_calculator': calculator_status,
                'feature': 'US-06: Matching Score',
                'user_count': user_count,
                'resume_count': resume_count,
                'jd_count': jd_count,
                'keyword_count': keyword_count,
                'matching_score_count': matching_score_count
            },
            'features': {
                'us01_registration': 'active' if auth_bp else 'not_available',
                'us02_jwt_auth': 'active' if auth_bp else 'not_available',
                'us03_resume_upload': 'active' if Resume else 'not_available',
                'us04_jd_upload': 'active' if JobDescription else 'not_available',
                'us05_keyword_parsing': 'active' if Keyword else 'not_available',
                'us06_matching_score': 'active'
            },
            'matching_config': calculator_info,
            'processing_readiness': {
                'processed_resumes': processed_resumes,
                'processed_jds': processed_jds,
                'ready_for_matching': processed_resumes > 0 and processed_jds > 0,
                'potential_matches': processed_resumes * processed_jds
            },
            'matching_stats': {
                'total_scores': matching_score_count,
                'calculation_methods': ['jaccard', 'weighted', 'hybrid'],
                'supported_categories': ['skill', 'experience', 'education', 'technology', 'framework']
            },
            'timestamp': datetime.utcnow().isoformat()
        })
    
    # Global error handlers
    @app.errorhandler(404)
    def not_found(error):
        """Handle 404 errors globally"""
        return jsonify({
            'success': False,
            'message': 'API endpoint not found',
            'error': 'Not Found',
            'available_endpoints': {
                'calculate_match': 'POST /api/calculate_match',
                'matching_scores': 'GET /api/matching_scores',
                'best_matches': 'GET /api/best_matches',
                'recalculate_all': 'POST /api/recalculate_all',
                'health': 'GET /health',
                'home': 'GET /'
            }
        }), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        """Handle 500 errors globally"""
        if db:
            db.session.rollback()
        return jsonify({
            'success': False,
            'message': 'Internal server error',
            'error': 'Internal Server Error'
        }), 500
    
    @app.errorhandler(400)
    def bad_request(error):
        """Handle 400 errors globally"""
        return jsonify({
            'success': False,
            'message': 'Bad request',
            'error': 'Bad Request'
        }), 400
    
    return app

def main():
    """
    Main function to run the Flask application
    """
    print("🚀 Starting US-06: Matching Score Application")
    print("=" * 60)
    
    # Create Flask app
    app = create_app()
    
    # Create database tables
    if db:
        with app.app_context():
            try:
                db.create_all()
                print("✅ Database tables created/verified")
            except Exception as e:
                print(f"⚠️  Database setup warning: {e}")
    
    # Test matching calculator
    print("\n🧮 Testing Matching Calculator:")
    try:
        calculator = get_matching_calculator()
        print(f"✅ Matching calculator initialized with algorithm v{calculator.algorithm_version}")
        print(f"✅ Keyword weights configured: {len(calculator.keyword_weights)} categories")
        
    except Exception as e:
        print(f"⚠️  Matching calculator warning: {e}")
    
    # Print startup information
    print("\n📋 US-06 Features Available:")
    print("• Jaccard Similarity Calculation")
    print("• Weighted Keyword Matching")
    print("• Hybrid Matching Algorithm")
    print("• Progress Bar Visualization")
    print("• Color-coded Match Indicators")
    print("• Category-specific Matching (Skills, Experience, Education)")
    print("• JWT Authentication (from US-02)")
    print("• User Management (from US-01)")
    
    print("\n🔗 API Endpoints:")
    print("• Home: GET /")
    print("• Health: GET /health")
    print("• Calculate Match: POST /api/calculate_match")
    print("• List Matching Scores: GET /api/matching_scores")
    print("• Get Matching Score: GET /api/matching_scores/<id>")
    print("• Best Matches: GET /api/best_matches")
    print("• Recalculate All: POST /api/recalculate_all")
    print("• Matching Health: GET /api/matching_health")
    
    print("\n🔐 Authentication:")
    print("• Login: POST /api/login")
    print("• Register: POST /api/register")
    print("• All matching endpoints require JWT authentication")
    
    print("\n📊 Matching Configuration:")
    print("• Calculation Methods: Jaccard, Weighted, Hybrid")
    print("• Progress Bar Display: Color-coded indicators")
    print("• Category Breakdown: Skills, Experience, Education")
    print("• Automatic Score Saving: Database persistence")
    
    print("\n🌐 Starting server...")
    print("Access the API at: http://localhost:5000")
    print("Frontend Interface: file:///path/to/US-06-Matching-Score/frontend/us06_matching.html")
    print("API Documentation: http://localhost:5000/")
    
    # Run the application
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True
    )

if __name__ == '__main__':
    main()
