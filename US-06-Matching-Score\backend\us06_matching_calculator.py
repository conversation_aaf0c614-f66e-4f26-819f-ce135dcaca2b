"""
US-06: Matching Score - Matching Calculator Utility
==================================================

This file contains the matching calculation logic using Jaccard similarity and other algorithms.
It compares keywords from resumes and job descriptions to compute matching scores.

Tech Stack: Python, SQLAlchemy
Dependencies: US-05 (Keyword model)
"""

import os
import sys
import time
import json
from typing import List, Dict, Tuple, Set, Optional
from collections import defaultdict, Counter
from decimal import Decimal

# Add paths for imports from previous US
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-01-User-Registration', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-03-Resume-Upload', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-04-JD-Upload', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-05-Keyword-Parsing', 'backend'))

try:
    from us01_user_model import db
    from us03_resume_model import Resume
    from us04_jd_model import JobDescription
    from us05_keyword_model import Keyword
    from us06_matching_model import MatchingScore
except ImportError as e:
    print(f"⚠️  Import warning: {e}")
    print("Some features may not work without previous US components")

class MatchingCalculator:
    """
    Matching calculator for resume-job description comparison
    
    This class provides methods to calculate matching scores between resumes
    and job descriptions using various algorithms including Jaccard similarity.
    """
    
    def __init__(self, algorithm_version='1.0'):
        """
        Initialize the matching calculator
        
        Args:
            algorithm_version (str): Version of the matching algorithm
        """
        self.algorithm_version = algorithm_version
        
        # Keyword type weights for scoring
        self.keyword_weights = {
            'technology': 1.5,      # High weight for technical skills
            'framework': 1.4,       # High weight for frameworks
            'skill': 1.3,           # High weight for skills
            'tool': 1.2,            # Medium-high weight for tools
            'experience': 1.1,      # Medium weight for experience
            'education': 1.0,       # Standard weight for education
            'certification': 1.2,   # Medium-high weight for certifications
            'language': 1.1,        # Medium weight for programming languages
            'general': 0.8          # Lower weight for general terms
        }
    
    def calculate_jaccard_similarity(self, set1: Set[str], set2: Set[str]) -> float:
        """
        Calculate Jaccard similarity coefficient between two sets
        
        Jaccard similarity = |A ∩ B| / |A ∪ B|
        
        Args:
            set1 (Set[str]): First set of keywords
            set2 (Set[str]): Second set of keywords
            
        Returns:
            float: Jaccard similarity coefficient (0.0-1.0)
        """
        if not set1 and not set2:
            return 1.0  # Both empty sets are identical
        
        if not set1 or not set2:
            return 0.0  # One empty set means no similarity
        
        intersection = set1.intersection(set2)
        union = set1.union(set2)
        
        return len(intersection) / len(union) if union else 0.0
    
    def calculate_weighted_similarity(self, resume_keywords: List[Dict], jd_keywords: List[Dict]) -> float:
        """
        Calculate weighted similarity considering keyword types and frequencies
        
        Args:
            resume_keywords (List[Dict]): Resume keywords with metadata
            jd_keywords (List[Dict]): Job description keywords with metadata
            
        Returns:
            float: Weighted similarity score (0.0-1.0)
        """
        # Create weighted keyword maps
        resume_weighted = defaultdict(float)
        jd_weighted = defaultdict(float)
        
        # Calculate weighted scores for resume keywords
        for kw in resume_keywords:
            keyword = kw['keyword'].lower()
            weight = self.keyword_weights.get(kw['keyword_type'], 1.0)
            frequency = kw.get('frequency', 1)
            confidence = kw.get('confidence_score', 0.8)
            
            resume_weighted[keyword] += weight * frequency * confidence
        
        # Calculate weighted scores for JD keywords
        for kw in jd_keywords:
            keyword = kw['keyword'].lower()
            weight = self.keyword_weights.get(kw['keyword_type'], 1.0)
            frequency = kw.get('frequency', 1)
            confidence = kw.get('confidence_score', 0.8)
            
            jd_weighted[keyword] += weight * frequency * confidence
        
        # Calculate similarity using weighted intersection
        common_keywords = set(resume_weighted.keys()).intersection(set(jd_weighted.keys()))
        
        if not common_keywords:
            return 0.0
        
        # Calculate weighted intersection and union
        intersection_weight = sum(
            min(resume_weighted[kw], jd_weighted[kw]) for kw in common_keywords
        )
        
        union_weight = sum(resume_weighted.values()) + sum(jd_weighted.values()) - intersection_weight
        
        return intersection_weight / union_weight if union_weight > 0 else 0.0
    
    def get_keywords_by_document(self, user_id: str, resume_id: str = None, job_description_id: str = None) -> List[Dict]:
        """
        Get keywords for a specific document
        
        Args:
            user_id (str): User ID
            resume_id (str, optional): Resume ID
            job_description_id (str, optional): Job description ID
            
        Returns:
            List[Dict]: List of keyword dictionaries
        """
        try:
            keywords = Keyword.get_by_document(
                user_id=user_id,
                resume_id=resume_id,
                job_description_id=job_description_id
            )
            
            return [kw.to_dict() for kw in keywords]
            
        except Exception as e:
            print(f"Error getting keywords: {e}")
            return []
    
    def analyze_keyword_overlap(self, resume_keywords: List[Dict], jd_keywords: List[Dict]) -> Dict:
        """
        Analyze keyword overlap between resume and job description
        
        Args:
            resume_keywords (List[Dict]): Resume keywords
            jd_keywords (List[Dict]): Job description keywords
            
        Returns:
            Dict: Overlap analysis results
        """
        # Extract keyword sets
        resume_set = {kw['keyword'].lower() for kw in resume_keywords}
        jd_set = {kw['keyword'].lower() for kw in jd_keywords}
        
        # Calculate overlaps
        matched = resume_set.intersection(jd_set)
        missing = jd_set - resume_set  # Keywords in JD but not in resume
        extra = resume_set - jd_set    # Keywords in resume but not in JD
        
        # Group by keyword type
        matched_by_type = defaultdict(list)
        missing_by_type = defaultdict(list)
        extra_by_type = defaultdict(list)
        
        # Categorize matched keywords
        for kw in resume_keywords:
            if kw['keyword'].lower() in matched:
                matched_by_type[kw['keyword_type']].append(kw['keyword'])
        
        # Categorize missing keywords
        for kw in jd_keywords:
            if kw['keyword'].lower() in missing:
                missing_by_type[kw['keyword_type']].append(kw['keyword'])
        
        # Categorize extra keywords
        for kw in resume_keywords:
            if kw['keyword'].lower() in extra:
                extra_by_type[kw['keyword_type']].append(kw['keyword'])
        
        return {
            'matched_keywords': list(matched),
            'missing_keywords': list(missing),
            'extra_keywords': list(extra),
            'matched_by_type': dict(matched_by_type),
            'missing_by_type': dict(missing_by_type),
            'extra_by_type': dict(extra_by_type),
            'overlap_count': len(matched),
            'resume_keyword_count': len(resume_set),
            'jd_keyword_count': len(jd_set)
        }
    
    def calculate_category_matches(self, resume_keywords: List[Dict], jd_keywords: List[Dict]) -> Dict[str, float]:
        """
        Calculate match percentages by keyword category
        
        Args:
            resume_keywords (List[Dict]): Resume keywords
            jd_keywords (List[Dict]): Job description keywords
            
        Returns:
            Dict[str, float]: Match percentages by category
        """
        categories = ['skill', 'technology', 'experience', 'education', 'certification', 'tool', 'framework']
        category_matches = {}
        
        for category in categories:
            # Get keywords for this category
            resume_cat = {kw['keyword'].lower() for kw in resume_keywords if kw['keyword_type'] == category}
            jd_cat = {kw['keyword'].lower() for kw in jd_keywords if kw['keyword_type'] == category}
            
            if not jd_cat:  # No requirements in this category
                category_matches[category] = 100.0
            elif not resume_cat:  # No skills in this category
                category_matches[category] = 0.0
            else:
                # Calculate match percentage
                matched = resume_cat.intersection(jd_cat)
                match_percentage = (len(matched) / len(jd_cat)) * 100
                category_matches[category] = min(100.0, match_percentage)
        
        return category_matches
    
    def calculate_overall_match(self, resume_id: str, job_description_id: str, user_id: str, 
                              method: str = 'jaccard') -> Tuple[Optional[Dict], Optional[str]]:
        """
        Calculate overall matching score between a resume and job description
        
        Args:
            resume_id (str): Resume ID
            job_description_id (str): Job description ID
            user_id (str): User ID
            method (str): Calculation method ('jaccard', 'weighted', 'hybrid')
            
        Returns:
            Tuple[Optional[Dict], Optional[str]]: (matching_data, error_message)
        """
        start_time = time.time()
        
        try:
            # Get keywords for both documents
            resume_keywords = self.get_keywords_by_document(user_id, resume_id=resume_id)
            jd_keywords = self.get_keywords_by_document(user_id, job_description_id=job_description_id)
            
            if not resume_keywords:
                return None, "No keywords found for resume. Please ensure keywords have been extracted."
            
            if not jd_keywords:
                return None, "No keywords found for job description. Please ensure keywords have been extracted."
            
            # Analyze keyword overlap
            overlap_analysis = self.analyze_keyword_overlap(resume_keywords, jd_keywords)
            
            # Calculate similarity scores
            resume_set = {kw['keyword'].lower() for kw in resume_keywords}
            jd_set = {kw['keyword'].lower() for kw in jd_keywords}
            
            jaccard_similarity = self.calculate_jaccard_similarity(resume_set, jd_set)
            weighted_similarity = self.calculate_weighted_similarity(resume_keywords, jd_keywords)
            
            # Calculate category-specific matches
            category_matches = self.calculate_category_matches(resume_keywords, jd_keywords)
            
            # Calculate overall match percentage based on method
            if method == 'jaccard':
                overall_match = jaccard_similarity * 100
            elif method == 'weighted':
                overall_match = weighted_similarity * 100
            elif method == 'hybrid':
                # Combine Jaccard and weighted similarity
                overall_match = (jaccard_similarity * 0.6 + weighted_similarity * 0.4) * 100
            else:
                overall_match = jaccard_similarity * 100  # Default to Jaccard
            
            # Calculate processing time
            processing_time = int((time.time() - start_time) * 1000)  # Convert to milliseconds
            
            # Prepare matching data
            matching_data = {
                'overall_match_percentage': round(overall_match, 2),
                'jaccard_similarity': round(jaccard_similarity, 4),
                'keyword_overlap_count': overlap_analysis['overlap_count'],
                'resume_keyword_count': overlap_analysis['resume_keyword_count'],
                'jd_keyword_count': overlap_analysis['jd_keyword_count'],
                'skill_match_percentage': round(category_matches.get('skill', 0.0), 2),
                'experience_match_percentage': round(category_matches.get('experience', 0.0), 2),
                'education_match_percentage': round(category_matches.get('education', 0.0), 2),
                'matched_keywords': overlap_analysis['matched_keywords'],
                'missing_keywords': overlap_analysis['missing_keywords'],
                'extra_keywords': overlap_analysis['extra_keywords'],
                'algorithm_version': self.algorithm_version,
                'calculation_method': method,
                'confidence_score': 0.85,  # Base confidence score
                'processing_time_ms': processing_time,
                'category_breakdown': category_matches,
                'overlap_analysis': overlap_analysis
            }
            
            return matching_data, None
            
        except Exception as e:
            return None, f"Error calculating match: {str(e)}"
    
    def save_matching_score(self, user_id: str, resume_id: str, job_description_id: str, 
                           matching_data: Dict) -> Tuple[Optional[MatchingScore], Optional[str]]:
        """
        Save matching score to database
        
        Args:
            user_id (str): User ID
            resume_id (str): Resume ID
            job_description_id (str): Job description ID
            matching_data (Dict): Matching calculation results
            
        Returns:
            Tuple[Optional[MatchingScore], Optional[str]]: (matching_score, error_message)
        """
        try:
            # Create matching score
            matching_score, error = MatchingScore.create_matching_score(
                user_id=user_id,
                resume_id=resume_id,
                job_description_id=job_description_id,
                overall_match_percentage=matching_data['overall_match_percentage'],
                jaccard_similarity=matching_data['jaccard_similarity'],
                keyword_overlap_count=matching_data['keyword_overlap_count'],
                resume_keyword_count=matching_data['resume_keyword_count'],
                jd_keyword_count=matching_data['jd_keyword_count'],
                skill_match_percentage=matching_data['skill_match_percentage'],
                experience_match_percentage=matching_data['experience_match_percentage'],
                education_match_percentage=matching_data['education_match_percentage'],
                matched_keywords=matching_data['matched_keywords'],
                missing_keywords=matching_data['missing_keywords'],
                extra_keywords=matching_data['extra_keywords'],
                algorithm_version=matching_data['algorithm_version'],
                calculation_method=matching_data['calculation_method'],
                confidence_score=matching_data['confidence_score'],
                processing_time_ms=matching_data['processing_time_ms']
            )
            
            return matching_score, error
            
        except Exception as e:
            return None, f"Error saving matching score: {str(e)}"

# Global calculator instance
_calculator_instance = None

def get_matching_calculator(algorithm_version='1.0'):
    """
    Get a singleton instance of MatchingCalculator
    
    Args:
        algorithm_version (str): Version of the matching algorithm
        
    Returns:
        MatchingCalculator: Configured calculator instance
    """
    global _calculator_instance
    
    if _calculator_instance is None:
        _calculator_instance = MatchingCalculator(algorithm_version=algorithm_version)
    
    return _calculator_instance
