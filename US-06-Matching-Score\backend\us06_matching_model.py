"""
US-06: Matching Score - Matching Score Model
===========================================

This file contains the SQLAlchemy model for matching scores between resumes and job descriptions.
It handles matching score data storage, validation, and relationships.

Tech Stack: SQLAlchemy, PostgreSQL
Dependencies: US-01 (User model), US-03 (Resume model), US-04 (JobDescription model), US-05 (Keyword model)
"""

import os
import sys
import uuid
import json
from datetime import datetime
from decimal import Decimal
from typing import List, Dict, Optional, Tuple

# Add paths for imports from previous US
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-01-User-Registration', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-03-Resume-Upload', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-04-JD-Upload', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-05-Keyword-Parsing', 'backend'))

try:
    from us01_user_model import db, User
except ImportError as e:
    print(f"⚠️  Import warning: {e}")
    print("Some features may not work without US-01")
    from flask_sqlalchemy import SQLAlchemy
    db = SQLAlchemy()

try:
    from us03_resume_model import Resume
except ImportError as e:
    print(f"⚠️  Import warning: {e}")
    print("Resume-related features may not work without US-03")
    Resume = None

try:
    from us04_jd_model import JobDescription
except ImportError as e:
    print(f"⚠️  Import warning: {e}")
    print("Job description-related features may not work without US-04")
    JobDescription = None

try:
    from us05_keyword_model import Keyword
except ImportError as e:
    print(f"⚠️  Import warning: {e}")
    print("Keyword-related features may not work without US-05")
    Keyword = None

class MatchingScore(db.Model):
    """
    Matching Score Model for US-06 Resume-JD Matching
    
    This model stores matching scores between resumes and job descriptions including:
    - Unique matching score ID (UUID)
    - User ID (foreign key to users table)
    - Resume and job description references
    - Overall match percentage and Jaccard similarity
    - Detailed matching breakdown by category
    - Keyword analysis (matched, missing, extra)
    - Algorithm metadata and timestamps
    """
    
    __tablename__ = 'matching_scores'
    
    # Primary key - using UUID for better security
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Foreign key to users table (from US-01)
    user_id = db.Column(db.String(36), db.ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    
    # Document references (resume vs job description comparison)
    resume_id = db.Column(db.String(36), db.ForeignKey('resumes.id', ondelete='CASCADE'), nullable=False)
    job_description_id = db.Column(db.String(36), db.ForeignKey('job_descriptions.id', ondelete='CASCADE'), nullable=False)
    
    # Matching scores and metrics
    overall_match_percentage = db.Column(db.Numeric(5, 2), nullable=False)
    jaccard_similarity = db.Column(db.Numeric(5, 4), default=Decimal('0.0000'), nullable=False)
    keyword_overlap_count = db.Column(db.Integer, default=0, nullable=False)
    resume_keyword_count = db.Column(db.Integer, default=0, nullable=False)
    jd_keyword_count = db.Column(db.Integer, default=0, nullable=False)
    
    # Detailed matching breakdown
    skill_match_percentage = db.Column(db.Numeric(5, 2), default=Decimal('0.00'), nullable=False)
    experience_match_percentage = db.Column(db.Numeric(5, 2), default=Decimal('0.00'), nullable=False)
    education_match_percentage = db.Column(db.Numeric(5, 2), default=Decimal('0.00'), nullable=False)
    
    # Keyword analysis (stored as JSON strings)
    matched_keywords = db.Column(db.Text, nullable=True)
    missing_keywords = db.Column(db.Text, nullable=True)
    extra_keywords = db.Column(db.Text, nullable=True)
    
    # Scoring algorithm metadata
    algorithm_version = db.Column(db.String(20), default='1.0', nullable=True)
    calculation_method = db.Column(db.String(50), default='jaccard', nullable=True)
    confidence_score = db.Column(db.Numeric(3, 2), default=Decimal('0.80'), nullable=False)
    
    # Processing metadata
    processing_time_ms = db.Column(db.Integer, nullable=True)
    last_calculated_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    # Status flags
    is_current = db.Column(db.Boolean, default=True, nullable=False)
    needs_recalculation = db.Column(db.Boolean, default=False, nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = db.relationship('User', backref=db.backref('matching_scores', lazy=True, cascade='all, delete-orphan'))
    
    if Resume:
        resume = db.relationship('Resume', backref=db.backref('matching_scores', lazy=True, cascade='all, delete-orphan'))
    
    if JobDescription:
        job_description = db.relationship('JobDescription', backref=db.backref('matching_scores', lazy=True, cascade='all, delete-orphan'))
    
    def __init__(self, user_id, resume_id, job_description_id, overall_match_percentage,
                 jaccard_similarity=0.0000, keyword_overlap_count=0, resume_keyword_count=0,
                 jd_keyword_count=0, skill_match_percentage=0.00, experience_match_percentage=0.00,
                 education_match_percentage=0.00, matched_keywords=None, missing_keywords=None,
                 extra_keywords=None, algorithm_version='1.0', calculation_method='jaccard',
                 confidence_score=0.80, processing_time_ms=None):
        """
        Initialize a new matching score
        
        Args:
            user_id (str): ID of the user who owns this matching score
            resume_id (str): ID of the resume
            job_description_id (str): ID of the job description
            overall_match_percentage (float): Overall match percentage (0.00-100.00)
            jaccard_similarity (float, optional): Jaccard similarity coefficient (0.0000-1.0000)
            keyword_overlap_count (int, optional): Number of overlapping keywords
            resume_keyword_count (int, optional): Total keywords in resume
            jd_keyword_count (int, optional): Total keywords in job description
            skill_match_percentage (float, optional): Skills match percentage
            experience_match_percentage (float, optional): Experience match percentage
            education_match_percentage (float, optional): Education match percentage
            matched_keywords (list, optional): List of matched keywords
            missing_keywords (list, optional): List of missing keywords
            extra_keywords (list, optional): List of extra keywords
            algorithm_version (str, optional): Version of matching algorithm
            calculation_method (str, optional): Calculation method used
            confidence_score (float, optional): Confidence in the match score
            processing_time_ms (int, optional): Processing time in milliseconds
        """
        self.user_id = user_id
        self.resume_id = resume_id
        self.job_description_id = job_description_id
        self.overall_match_percentage = Decimal(str(overall_match_percentage))
        self.jaccard_similarity = Decimal(str(jaccard_similarity))
        self.keyword_overlap_count = keyword_overlap_count
        self.resume_keyword_count = resume_keyword_count
        self.jd_keyword_count = jd_keyword_count
        self.skill_match_percentage = Decimal(str(skill_match_percentage))
        self.experience_match_percentage = Decimal(str(experience_match_percentage))
        self.education_match_percentage = Decimal(str(education_match_percentage))
        self.matched_keywords = json.dumps(matched_keywords) if matched_keywords else None
        self.missing_keywords = json.dumps(missing_keywords) if missing_keywords else None
        self.extra_keywords = json.dumps(extra_keywords) if extra_keywords else None
        self.algorithm_version = algorithm_version
        self.calculation_method = calculation_method
        self.confidence_score = Decimal(str(confidence_score))
        self.processing_time_ms = processing_time_ms
    
    def to_dict(self):
        """
        Convert matching score to dictionary for JSON serialization
        
        Returns:
            dict: Matching score data as dictionary
        """
        return {
            'id': self.id,
            'user_id': self.user_id,
            'resume_id': self.resume_id,
            'job_description_id': self.job_description_id,
            'overall_match_percentage': float(self.overall_match_percentage),
            'jaccard_similarity': float(self.jaccard_similarity),
            'keyword_overlap_count': self.keyword_overlap_count,
            'resume_keyword_count': self.resume_keyword_count,
            'jd_keyword_count': self.jd_keyword_count,
            'skill_match_percentage': float(self.skill_match_percentage),
            'experience_match_percentage': float(self.experience_match_percentage),
            'education_match_percentage': float(self.education_match_percentage),
            'matched_keywords': json.loads(self.matched_keywords) if self.matched_keywords else [],
            'missing_keywords': json.loads(self.missing_keywords) if self.missing_keywords else [],
            'extra_keywords': json.loads(self.extra_keywords) if self.extra_keywords else [],
            'algorithm_version': self.algorithm_version,
            'calculation_method': self.calculation_method,
            'confidence_score': float(self.confidence_score),
            'processing_time_ms': self.processing_time_ms,
            'last_calculated_at': self.last_calculated_at.isoformat() if self.last_calculated_at else None,
            'is_current': self.is_current,
            'needs_recalculation': self.needs_recalculation,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'match_category': self.get_match_category()
        }
    
    def get_match_category(self):
        """
        Get match category based on overall match percentage
        
        Returns:
            str: Match category ('excellent', 'good', 'fair', 'poor', 'very_poor')
        """
        percentage = float(self.overall_match_percentage)
        if percentage >= 80:
            return 'excellent'
        elif percentage >= 60:
            return 'good'
        elif percentage >= 40:
            return 'fair'
        elif percentage >= 20:
            return 'poor'
        else:
            return 'very_poor'
    
    def get_match_color(self):
        """
        Get color code for progress bar based on match category
        
        Returns:
            str: Color code for UI display
        """
        category = self.get_match_category()
        color_map = {
            'excellent': '#28a745',  # Green
            'good': '#17a2b8',       # Blue
            'fair': '#ffc107',       # Yellow
            'poor': '#fd7e14',       # Orange
            'very_poor': '#dc3545'   # Red
        }
        return color_map.get(category, '#6c757d')  # Default gray
    
    @classmethod
    def create_matching_score(cls, user_id, resume_id, job_description_id, **kwargs):
        """
        Create a new matching score with validation
        
        Args:
            user_id (str): ID of the user
            resume_id (str): ID of the resume
            job_description_id (str): ID of the job description
            **kwargs: Additional matching score fields
            
        Returns:
            tuple: (MatchingScore object, error_message)
                   Returns (matching_score, None) on success
                   Returns (None, error_message) on failure
        """
        try:
            # Validate required fields
            if not user_id:
                return None, "User ID is required"
            
            if not resume_id:
                return None, "Resume ID is required"
            
            if not job_description_id:
                return None, "Job description ID is required"
            
            # Validate user exists
            user = User.query.get(user_id)
            if not user:
                return None, "User not found"
            
            # Validate resume exists and belongs to user
            if Resume:
                resume = Resume.query.filter_by(id=resume_id, user_id=user_id).first()
                if not resume:
                    return None, "Resume not found or does not belong to user"
            
            # Validate job description exists and belongs to user
            if JobDescription:
                jd = JobDescription.query.filter_by(id=job_description_id, user_id=user_id).first()
                if not jd:
                    return None, "Job description not found or does not belong to user"
            
            # Validate overall match percentage
            overall_match = kwargs.get('overall_match_percentage', 0.0)
            if not (0.0 <= overall_match <= 100.0):
                return None, "Overall match percentage must be between 0.0 and 100.0"
            
            # Mark any existing scores as not current
            cls.query.filter_by(
                resume_id=resume_id,
                job_description_id=job_description_id,
                is_current=True
            ).update({'is_current': False})
            
            # Create matching score
            matching_score = cls(
                user_id=user_id,
                resume_id=resume_id,
                job_description_id=job_description_id,
                overall_match_percentage=overall_match,
                **kwargs
            )
            
            # Save to database
            db.session.add(matching_score)
            db.session.commit()
            
            return matching_score, None
            
        except Exception as e:
            db.session.rollback()
            return None, f"Error creating matching score: {str(e)}"
    
    @classmethod
    def get_current_score(cls, user_id, resume_id, job_description_id):
        """
        Get the current matching score for a resume-JD pair
        
        Args:
            user_id (str): User ID
            resume_id (str): Resume ID
            job_description_id (str): Job description ID
            
        Returns:
            MatchingScore: Current matching score or None
        """
        return cls.query.filter_by(
            user_id=user_id,
            resume_id=resume_id,
            job_description_id=job_description_id,
            is_current=True
        ).first()
    
    @classmethod
    def get_by_user(cls, user_id, limit=None):
        """
        Get matching scores for a specific user
        
        Args:
            user_id (str): User ID
            limit (int, optional): Maximum number of results
            
        Returns:
            list: List of MatchingScore objects
        """
        query = cls.query.filter_by(user_id=user_id, is_current=True).order_by(cls.created_at.desc())
        
        if limit:
            query = query.limit(limit)
            
        return query.all()
    
    @classmethod
    def get_by_resume(cls, user_id, resume_id):
        """
        Get all current matching scores for a specific resume
        
        Args:
            user_id (str): User ID
            resume_id (str): Resume ID
            
        Returns:
            list: List of MatchingScore objects
        """
        return cls.query.filter_by(
            user_id=user_id,
            resume_id=resume_id,
            is_current=True
        ).order_by(cls.overall_match_percentage.desc()).all()
    
    @classmethod
    def get_by_job_description(cls, user_id, job_description_id):
        """
        Get all current matching scores for a specific job description
        
        Args:
            user_id (str): User ID
            job_description_id (str): Job description ID
            
        Returns:
            list: List of MatchingScore objects
        """
        return cls.query.filter_by(
            user_id=user_id,
            job_description_id=job_description_id,
            is_current=True
        ).order_by(cls.overall_match_percentage.desc()).all()
    
    def mark_for_recalculation(self):
        """
        Mark this matching score for recalculation
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.needs_recalculation = True
            self.updated_at = datetime.utcnow()
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error marking for recalculation: {e}")
            return False
    
    def delete(self):
        """
        Delete this matching score
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            db.session.delete(self)
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error deleting matching score: {e}")
            return False
    
    def __repr__(self):
        """String representation of the matching score"""
        return f'<MatchingScore {self.id}: {self.overall_match_percentage}% (Resume:{self.resume_id} vs JD:{self.job_description_id})>'
