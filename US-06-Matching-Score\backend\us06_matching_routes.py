"""
US-06: Matching Score - Matching Routes
======================================

This file contains the Flask routes for matching score calculation and management.
It handles resume-JD matching, score retrieval, and progress display.

Tech Stack: Flask, Flask-JWT-Extended, SQLAlchemy
Dependencies: US-01 (User model), US-02 (JWT), US-03 (Resume model), US-04 (JobDescription model), US-05 (Keyword model)
"""

import os
import sys
from datetime import datetime
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity

# Add paths for imports from previous US
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-01-User-Registration', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-03-Resume-Upload', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-04-JD-Upload', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-05-Keyword-Parsing', 'backend'))

try:
    from us01_user_model import User, db
    from us03_resume_model import Resume
    from us04_jd_model import JobDescription
    from us05_keyword_model import Keyword
    from us06_matching_model import MatchingScore
    from us06_matching_calculator import get_matching_calculator
except ImportError as e:
    print(f"⚠️  Import warning: {e}")
    print("Some features may not work without previous US components")

# Create Blueprint for matching routes
matching_bp = Blueprint('matching', __name__, url_prefix='/api')

@matching_bp.route('/calculate_match', methods=['POST'])
@jwt_required()
def calculate_match():
    """
    Calculate Matching Score Endpoint
    
    POST /api/calculate_match
    
    Calculate matching score between a resume and job description using Jaccard similarity.
    
    Required Headers:
        Authorization: Bearer <access_token>
    
    Request Body (JSON):
        {
            "resume_id": "uuid-here",
            "job_description_id": "uuid-here",
            "calculation_method": "jaccard|weighted|hybrid (optional, default: jaccard)"
        }
    
    Returns:
        JSON response with matching score and detailed analysis
    """
    try:
        # Get current user from JWT token
        current_user_id = get_jwt_identity()
        
        # Verify user exists and is active
        user = User.query.get(current_user_id)
        if not user or not user.is_active:
            return jsonify({
                'success': False,
                'message': 'User not found or inactive'
            }), 401
        
        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400
        
        resume_id = data.get('resume_id')
        job_description_id = data.get('job_description_id')
        calculation_method = data.get('calculation_method', 'jaccard')
        
        # Validate input
        if not resume_id:
            return jsonify({
                'success': False,
                'message': 'Resume ID is required'
            }), 400
        
        if not job_description_id:
            return jsonify({
                'success': False,
                'message': 'Job description ID is required'
            }), 400
        
        # Validate calculation method
        valid_methods = ['jaccard', 'weighted', 'hybrid']
        if calculation_method not in valid_methods:
            return jsonify({
                'success': False,
                'message': f'Invalid calculation method. Must be one of: {", ".join(valid_methods)}'
            }), 400
        
        # Verify documents exist and belong to user
        resume = Resume.query.filter_by(id=resume_id, user_id=current_user_id).first()
        if not resume:
            return jsonify({
                'success': False,
                'message': 'Resume not found or does not belong to user'
            }), 404
        
        jd = JobDescription.query.filter_by(id=job_description_id, user_id=current_user_id).first()
        if not jd:
            return jsonify({
                'success': False,
                'message': 'Job description not found or does not belong to user'
            }), 404
        
        # Check if keywords have been extracted
        if not resume.keywords_extracted:
            return jsonify({
                'success': False,
                'message': 'Resume keywords have not been extracted. Please run keyword extraction first.'
            }), 400
        
        if not jd.keywords_extracted:
            return jsonify({
                'success': False,
                'message': 'Job description keywords have not been extracted. Please run keyword extraction first.'
            }), 400
        
        # Calculate matching score
        calculator = get_matching_calculator()
        matching_data, error = calculator.calculate_overall_match(
            resume_id=resume_id,
            job_description_id=job_description_id,
            user_id=current_user_id,
            method=calculation_method
        )
        
        if error:
            return jsonify({
                'success': False,
                'message': error
            }), 400
        
        # Save matching score to database
        matching_score, save_error = calculator.save_matching_score(
            user_id=current_user_id,
            resume_id=resume_id,
            job_description_id=job_description_id,
            matching_data=matching_data
        )
        
        if save_error:
            return jsonify({
                'success': False,
                'message': f'Error saving matching score: {save_error}'
            }), 500
        
        # Success response
        return jsonify({
            'success': True,
            'message': f'Matching score calculated successfully using {calculation_method} method',
            'matching_score': matching_score.to_dict(),
            'calculation_details': {
                'method': calculation_method,
                'processing_time_ms': matching_data['processing_time_ms'],
                'algorithm_version': matching_data['algorithm_version'],
                'category_breakdown': matching_data['category_breakdown']
            },
            'documents': {
                'resume': {
                    'id': resume.id,
                    'title': resume.resume_title or resume.original_filename
                },
                'job_description': {
                    'id': jd.id,
                    'title': jd.title,
                    'company': jd.company_name
                }
            }
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error calculating matching score: {str(e)}'
        }), 500

@matching_bp.route('/matching_scores', methods=['GET'])
@jwt_required()
def get_matching_scores():
    """
    Get Matching Scores List Endpoint
    
    GET /api/matching_scores
    
    Returns a list of matching scores for the current user.
    
    Query Parameters:
        resume_id (str, optional): Filter by resume ID
        job_description_id (str, optional): Filter by job description ID
        limit (int, optional): Maximum number of results (default: 50)
        min_score (float, optional): Minimum match percentage filter
    
    Returns:
        JSON response with list of matching scores
    """
    try:
        # Get current user from JWT token
        current_user_id = get_jwt_identity()
        
        # Get query parameters
        resume_id = request.args.get('resume_id')
        job_description_id = request.args.get('job_description_id')
        limit = request.args.get('limit', 50, type=int)
        min_score = request.args.get('min_score', type=float)
        
        # Validate limit
        if limit > 200:
            limit = 200
        
        # Get matching scores
        if resume_id:
            matching_scores = MatchingScore.get_by_resume(current_user_id, resume_id)
        elif job_description_id:
            matching_scores = MatchingScore.get_by_job_description(current_user_id, job_description_id)
        else:
            matching_scores = MatchingScore.get_by_user(current_user_id, limit=limit)
        
        # Apply minimum score filter
        if min_score is not None:
            matching_scores = [ms for ms in matching_scores if float(ms.overall_match_percentage) >= min_score]
        
        # Convert to dictionary format
        scores_list = [ms.to_dict() for ms in matching_scores[:limit]]
        
        # Add document information
        for score in scores_list:
            # Get resume info
            resume = Resume.query.get(score['resume_id'])
            if resume:
                score['resume_info'] = {
                    'title': resume.resume_title or resume.original_filename,
                    'filename': resume.original_filename
                }
            
            # Get job description info
            jd = JobDescription.query.get(score['job_description_id'])
            if jd:
                score['job_description_info'] = {
                    'title': jd.title,
                    'company': jd.company_name
                }
        
        return jsonify({
            'success': True,
            'message': f'Retrieved {len(scores_list)} matching scores',
            'matching_scores': scores_list,
            'total_count': len(scores_list),
            'filters': {
                'resume_id': resume_id,
                'job_description_id': job_description_id,
                'min_score': min_score,
                'limit': limit
            }
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error retrieving matching scores: {str(e)}'
        }), 500

@matching_bp.route('/matching_scores/<score_id>', methods=['GET'])
@jwt_required()
def get_matching_score(score_id):
    """
    Get Single Matching Score Endpoint
    
    GET /api/matching_scores/<score_id>
    
    Returns details of a specific matching score.
    
    Returns:
        JSON response with matching score details
    """
    try:
        # Get current user from JWT token
        current_user_id = get_jwt_identity()
        
        # Get matching score (filtered by user for security)
        matching_score = MatchingScore.query.filter_by(id=score_id, user_id=current_user_id).first()
        
        if not matching_score:
            return jsonify({
                'success': False,
                'message': 'Matching score not found'
            }), 404
        
        # Get document information
        resume = Resume.query.get(matching_score.resume_id)
        jd = JobDescription.query.get(matching_score.job_description_id)
        
        score_data = matching_score.to_dict()
        
        # Add document information
        if resume:
            score_data['resume_info'] = {
                'title': resume.resume_title or resume.original_filename,
                'filename': resume.original_filename,
                'upload_date': resume.created_at.isoformat() if resume.created_at else None
            }
        
        if jd:
            score_data['job_description_info'] = {
                'title': jd.title,
                'company': jd.company_name,
                'location': jd.location,
                'upload_date': jd.created_at.isoformat() if jd.created_at else None
            }
        
        return jsonify({
            'success': True,
            'message': 'Matching score retrieved successfully',
            'matching_score': score_data
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error retrieving matching score: {str(e)}'
        }), 500

@matching_bp.route('/best_matches', methods=['GET'])
@jwt_required()
def get_best_matches():
    """
    Get Best Matches Endpoint
    
    GET /api/best_matches
    
    Returns the best matching scores for the user.
    
    Query Parameters:
        limit (int, optional): Maximum number of results (default: 10)
        min_score (float, optional): Minimum match percentage (default: 50.0)
    
    Returns:
        JSON response with best matching scores
    """
    try:
        # Get current user from JWT token
        current_user_id = get_jwt_identity()
        
        # Get query parameters
        limit = request.args.get('limit', 10, type=int)
        min_score = request.args.get('min_score', 50.0, type=float)
        
        # Validate limit
        if limit > 50:
            limit = 50
        
        # Get all matching scores for user, ordered by match percentage
        matching_scores = MatchingScore.query.filter_by(
            user_id=current_user_id,
            is_current=True
        ).filter(
            MatchingScore.overall_match_percentage >= min_score
        ).order_by(
            MatchingScore.overall_match_percentage.desc()
        ).limit(limit).all()
        
        # Convert to dictionary format with document info
        best_matches = []
        for ms in matching_scores:
            score_data = ms.to_dict()
            
            # Get document information
            resume = Resume.query.get(ms.resume_id)
            jd = JobDescription.query.get(ms.job_description_id)
            
            if resume:
                score_data['resume_info'] = {
                    'title': resume.resume_title or resume.original_filename,
                    'filename': resume.original_filename
                }
            
            if jd:
                score_data['job_description_info'] = {
                    'title': jd.title,
                    'company': jd.company_name,
                    'location': jd.location
                }
            
            best_matches.append(score_data)
        
        return jsonify({
            'success': True,
            'message': f'Retrieved {len(best_matches)} best matches',
            'best_matches': best_matches,
            'filters': {
                'min_score': min_score,
                'limit': limit
            }
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error retrieving best matches: {str(e)}'
        }), 500

@matching_bp.route('/recalculate_all', methods=['POST'])
@jwt_required()
def recalculate_all_scores():
    """
    Recalculate All Scores Endpoint
    
    POST /api/recalculate_all
    
    Recalculate all matching scores for the current user.
    
    Request Body (JSON):
        {
            "calculation_method": "jaccard|weighted|hybrid (optional, default: jaccard)",
            "force_recalculate": false (optional, default: false)
        }
    
    Returns:
        JSON response with recalculation results
    """
    try:
        # Get current user from JWT token
        current_user_id = get_jwt_identity()
        
        # Get request data
        data = request.get_json() or {}
        calculation_method = data.get('calculation_method', 'jaccard')
        force_recalculate = data.get('force_recalculate', False)
        
        # Get all processed documents for user
        resumes = Resume.query.filter_by(user_id=current_user_id, keywords_extracted=True).all()
        jds = JobDescription.query.filter_by(user_id=current_user_id, keywords_extracted=True).all()
        
        if not resumes or not jds:
            return jsonify({
                'success': False,
                'message': 'Need at least one processed resume and one processed job description'
            }), 400
        
        # Initialize calculator
        calculator = get_matching_calculator()
        
        # Process results
        results = {
            'scores_calculated': 0,
            'scores_updated': 0,
            'errors': []
        }
        
        # Calculate scores for all combinations
        for resume in resumes:
            for jd in jds:
                try:
                    # Check if score already exists
                    existing_score = MatchingScore.get_current_score(
                        current_user_id, resume.id, jd.id
                    )
                    
                    if existing_score and not force_recalculate:
                        continue  # Skip if already calculated
                    
                    # Calculate matching score
                    matching_data, error = calculator.calculate_overall_match(
                        resume_id=resume.id,
                        job_description_id=jd.id,
                        user_id=current_user_id,
                        method=calculation_method
                    )
                    
                    if error:
                        results['errors'].append(f"Resume {resume.id} vs JD {jd.id}: {error}")
                        continue
                    
                    # Save matching score
                    matching_score, save_error = calculator.save_matching_score(
                        user_id=current_user_id,
                        resume_id=resume.id,
                        job_description_id=jd.id,
                        matching_data=matching_data
                    )
                    
                    if save_error:
                        results['errors'].append(f"Save error for Resume {resume.id} vs JD {jd.id}: {save_error}")
                    else:
                        if existing_score:
                            results['scores_updated'] += 1
                        else:
                            results['scores_calculated'] += 1
                    
                except Exception as e:
                    results['errors'].append(f"Resume {resume.id} vs JD {jd.id}: {str(e)}")
        
        return jsonify({
            'success': True,
            'message': f'Recalculated {results["scores_calculated"]} new scores and updated {results["scores_updated"]} existing scores',
            'results': results
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error recalculating scores: {str(e)}'
        }), 500

# Health check endpoint for the matching service
@matching_bp.route('/matching_health', methods=['GET'])
def matching_health_check():
    """
    Health check endpoint for the matching service
    
    Returns:
        JSON response indicating service status
    """
    # Test calculator availability
    calculator_status = 'unknown'
    try:
        calculator = get_matching_calculator()
        calculator_status = 'available'
    except Exception as e:
        calculator_status = f'error: {str(e)}'
    
    return jsonify({
        'success': True,
        'message': 'Matching service is running',
        'service': 'US-06 Matching Score',
        'calculator_status': calculator_status,
        'supported_methods': ['jaccard', 'weighted', 'hybrid'],
        'timestamp': datetime.utcnow().isoformat()
    }), 200
