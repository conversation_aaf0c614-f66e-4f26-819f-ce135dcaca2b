"""
US-06: Matching Score - Database Initialization
==============================================

This script initializes the database for US-06 Matching Score feature.
It creates the matching_scores table and sets up the necessary relationships.

Tech Stack: Python, SQLAlchemy, PostgreSQL
Dependencies: US-01 (User model), US-03 (Resume model), US-04 (JobDescription model), US-05 (Keyword model)

Usage:
    python us06_init_db.py

Environment Variables Required:
    - DATABASE_URL or individual DB connection parameters
    - See README_US06.md for complete setup instructions
"""

import os
import sys
from datetime import datetime

# Add paths for imports from previous US
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-01-User-Registration', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-03-Resume-Upload', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-04-JD-Upload', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-05-Keyword-Parsing', 'backend'))

try:
    from us01_user_model import db, User
    print("✅ Successfully imported User model from US-01")
except ImportError as e:
    print(f"❌ Error importing from US-01: {e}")
    print("Please ensure US-01 is properly set up before running US-06")
    sys.exit(1)

try:
    from us03_resume_model import Resume
    print("✅ Successfully imported Resume model from US-03")
except ImportError as e:
    print(f"❌ Error importing from US-03: {e}")
    print("Please ensure US-03 is properly set up before running US-06")
    sys.exit(1)

try:
    from us04_jd_model import JobDescription
    print("✅ Successfully imported JobDescription model from US-04")
except ImportError as e:
    print(f"❌ Error importing from US-04: {e}")
    print("Please ensure US-04 is properly set up before running US-06")
    sys.exit(1)

try:
    from us05_keyword_model import Keyword
    print("✅ Successfully imported Keyword model from US-05")
except ImportError as e:
    print(f"❌ Error importing from US-05: {e}")
    print("Please ensure US-05 is properly set up before running US-06")
    sys.exit(1)

def create_matching_scores_table():
    """
    Create the matching_scores table using SQLAlchemy
    
    This function creates the table structure that matches
    the SQL schema defined in us06_schema.sql
    """
    try:
        # Import the MatchingScore model
        from us06_matching_model import MatchingScore
        
        # Create all tables (this will create matching_scores table)
        db.create_all()
        
        print("✅ Matching scores table created successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error creating matching_scores table: {e}")
        return False

def verify_database_setup():
    """
    Verify that the database setup is correct
    
    Returns:
        bool: True if setup is valid, False otherwise
    """
    try:
        # Check if users table exists (from US-01)
        users_count = User.query.count()
        print(f"✅ Users table verified - {users_count} users found")
        
        # Check if resumes table exists (from US-03)
        resumes_count = Resume.query.count()
        print(f"✅ Resumes table verified - {resumes_count} resumes found")
        
        # Check if job_descriptions table exists (from US-04)
        jd_count = JobDescription.query.count()
        print(f"✅ Job descriptions table verified - {jd_count} job descriptions found")
        
        # Check if keywords table exists (from US-05)
        keywords_count = Keyword.query.count()
        print(f"✅ Keywords table verified - {keywords_count} keywords found")
        
        # Check if matching_scores table exists
        from us06_matching_model import MatchingScore
        scores_count = MatchingScore.query.count()
        print(f"✅ Matching scores table verified - {scores_count} matching scores found")
        
        return True
        
    except Exception as e:
        print(f"❌ Database verification failed: {e}")
        return False

def create_sample_data():
    """
    Create sample matching score data for testing
    
    This is optional and only runs if explicitly requested
    """
    try:
        from us06_matching_model import MatchingScore
        
        # Check if we have any users to associate with
        sample_user = User.query.first()
        if not sample_user:
            print("⚠️  No users found. Please register a user first using US-01")
            return False
        
        # Check if we have resumes and job descriptions
        sample_resume = Resume.query.filter_by(user_id=sample_user.id).first()
        sample_jd = JobDescription.query.filter_by(user_id=sample_user.id).first()
        
        if not sample_resume:
            print("⚠️  No resumes found. Please upload a resume using US-03")
            return False
        
        if not sample_jd:
            print("⚠️  No job descriptions found. Please upload a job description using US-04")
            return False
        
        # Check if sample data already exists
        existing_score = MatchingScore.query.filter_by(
            user_id=sample_user.id,
            resume_id=sample_resume.id,
            job_description_id=sample_jd.id
        ).first()
        
        if existing_score:
            print("ℹ️  Sample matching score data already exists")
            return True
        
        # Create sample matching score
        sample_score = MatchingScore(
            user_id=sample_user.id,
            resume_id=sample_resume.id,
            job_description_id=sample_jd.id,
            overall_match_percentage=75.50,
            jaccard_similarity=0.6250,
            keyword_overlap_count=5,
            resume_keyword_count=8,
            jd_keyword_count=10,
            skill_match_percentage=80.00,
            experience_match_percentage=70.00,
            education_match_percentage=75.00,
            matched_keywords='["python", "flask", "postgresql", "software engineer", "bachelor degree"]',
            missing_keywords='["react", "docker", "aws", "senior level", "computer science"]',
            extra_keywords='["javascript", "html", "css"]',
            algorithm_version='1.0',
            calculation_method='jaccard',
            confidence_score=0.85
        )
        
        db.session.add(sample_score)
        db.session.commit()
        
        print("✅ Sample matching score created successfully")
        print(f"   Resume: {sample_resume.resume_title or sample_resume.original_filename}")
        print(f"   Job Description: {sample_jd.title}")
        print(f"   Match Percentage: {sample_score.overall_match_percentage}%")
        print(f"   User: {sample_user.email}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating sample data: {e}")
        db.session.rollback()
        return False

def test_matching_dependencies():
    """
    Test if matching calculation dependencies are available
    
    Returns:
        bool: True if dependencies are available, False otherwise
    """
    print("\n🔍 Testing matching calculation dependencies...")
    
    # Test if we have keywords to work with
    try:
        keyword_count = Keyword.query.count()
        if keyword_count > 0:
            print(f"✅ Keywords available for matching: {keyword_count} keywords found")
        else:
            print("⚠️  No keywords found. Run US-05 keyword extraction first")
        
        # Test if we have processed documents
        processed_resumes = Resume.query.filter_by(keywords_extracted=True).count()
        processed_jds = JobDescription.query.filter_by(keywords_extracted=True).count()
        
        print(f"✅ Processed resumes: {processed_resumes}")
        print(f"✅ Processed job descriptions: {processed_jds}")
        
        if processed_resumes == 0 or processed_jds == 0:
            print("⚠️  Need at least one processed resume and one processed job description for matching")
            
    except Exception as e:
        print(f"⚠️  Dependency check error: {e}")
    
    return True

def main():
    """
    Main function to initialize the database for US-06
    """
    print("🚀 Initializing US-06: Matching Score Database")
    print("=" * 60)
    
    # Step 1: Test matching dependencies
    print("\n📦 Step 1: Testing matching dependencies...")
    test_matching_dependencies()
    
    # Step 2: Create the matching_scores table
    print("\n📋 Step 2: Creating matching_scores table...")
    if not create_matching_scores_table():
        print("❌ Failed to create matching_scores table")
        return False
    
    # Step 3: Verify database setup
    print("\n🔍 Step 3: Verifying database setup...")
    if not verify_database_setup():
        print("❌ Database verification failed")
        return False
    
    # Step 4: Ask about sample data
    print("\n📊 Step 4: Sample data creation...")
    create_sample = input("Do you want to create sample matching score data? (y/N): ").lower().strip()
    
    if create_sample in ['y', 'yes']:
        if create_sample_data():
            print("✅ Sample data created successfully")
        else:
            print("⚠️  Sample data creation failed, but database setup is complete")
    else:
        print("ℹ️  Skipping sample data creation")
    
    print("\n🎉 US-06 Database initialization completed successfully!")
    print("\nNext steps:")
    print("1. Ensure US-05 has processed some documents (keywords extracted)")
    print("2. Start the Flask application: python us06_app.py")
    print("3. Test matching calculation: POST /api/calculate_match")
    print("4. Check the frontend interface: us06_matching.html")
    print("5. View matching scores with progress bars")
    
    return True

if __name__ == "__main__":
    # Set up the application context for database operations
    try:
        # Import the Flask app to get the application context
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))
        from us06_app import create_app
        
        app = create_app()
        with app.app_context():
            success = main()
            if not success:
                sys.exit(1)
                
    except ImportError as e:
        print(f"❌ Error importing Flask app: {e}")
        print("Please ensure the backend components are created first")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)
