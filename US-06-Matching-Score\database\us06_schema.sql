-- US-06: Matching Score - Database Schema
-- ========================================
-- 
-- This file contains the PostgreSQL database schema for US-06 Matching Score
-- It defines the matching_scores table structure and relationships
-- 
-- Tech Stack: PostgreSQL 13+
-- Dependencies: US-01 (users table), US-03 (resumes table), US-04 (job_descriptions table), US-05 (keywords table)

-- Note: This assumes the database and UUID extension are already created in US-01
-- If running independently, uncomment the following:
-- CREATE DATABASE dr_resume_db;
-- \c dr_resume_db;
-- CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create matching_scores table for US-06 Matching Score
CREATE TABLE IF NOT EXISTS matching_scores (
    -- Primary key using UUID for better security
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    
    -- Foreign key to users table (from US-01)
    user_id VARCHAR(36) NOT NULL,
    
    -- Document references (resume vs job description comparison)
    resume_id VARCHAR(36) NOT NULL,                         -- Reference to resumes table (US-03)
    job_description_id VARCHAR(36) NOT NULL,                -- Reference to job_descriptions table (US-04)
    
    -- Matching scores and metrics
    overall_match_percentage DECIMAL(5,2) NOT NULL,         -- Overall match percentage (0.00-100.00)
    jaccard_similarity DECIMAL(5,4) DEFAULT 0.0000,         -- Jaccard similarity coefficient (0.0000-1.0000)
    keyword_overlap_count INTEGER DEFAULT 0,                -- Number of overlapping keywords
    resume_keyword_count INTEGER DEFAULT 0,                 -- Total keywords in resume
    jd_keyword_count INTEGER DEFAULT 0,                     -- Total keywords in job description
    
    -- Detailed matching breakdown
    skill_match_percentage DECIMAL(5,2) DEFAULT 0.00,       -- Skills/technology match
    experience_match_percentage DECIMAL(5,2) DEFAULT 0.00,  -- Experience level match
    education_match_percentage DECIMAL(5,2) DEFAULT 0.00,   -- Education match
    
    -- Keyword analysis
    matched_keywords TEXT,                                   -- JSON array of matched keywords
    missing_keywords TEXT,                                   -- JSON array of missing keywords from JD
    extra_keywords TEXT,                                     -- JSON array of extra keywords in resume
    
    -- Scoring algorithm metadata
    algorithm_version VARCHAR(20) DEFAULT '1.0',            -- Version of matching algorithm
    calculation_method VARCHAR(50) DEFAULT 'jaccard',       -- jaccard, cosine, weighted, etc.
    confidence_score DECIMAL(3,2) DEFAULT 0.80,             -- Confidence in the match score
    
    -- Processing metadata
    processing_time_ms INTEGER,                             -- Time taken to calculate (milliseconds)
    last_calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- When score was last calculated
    
    -- Status flags
    is_current BOOLEAN DEFAULT TRUE,                         -- Is this the current/latest score
    needs_recalculation BOOLEAN DEFAULT FALSE,               -- Flag for when keywords change
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    
    -- Constraints
    CONSTRAINT fk_matching_scores_user_id 
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_matching_scores_resume_id 
        FOREIGN KEY (resume_id) REFERENCES resumes(id) ON DELETE CASCADE,
    CONSTRAINT fk_matching_scores_job_description_id 
        FOREIGN KEY (job_description_id) REFERENCES job_descriptions(id) ON DELETE CASCADE,
    CONSTRAINT chk_overall_match_percentage 
        CHECK (overall_match_percentage >= 0.00 AND overall_match_percentage <= 100.00),
    CONSTRAINT chk_jaccard_similarity 
        CHECK (jaccard_similarity >= 0.0000 AND jaccard_similarity <= 1.0000),
    CONSTRAINT chk_skill_match_percentage 
        CHECK (skill_match_percentage >= 0.00 AND skill_match_percentage <= 100.00),
    CONSTRAINT chk_experience_match_percentage 
        CHECK (experience_match_percentage >= 0.00 AND experience_match_percentage <= 100.00),
    CONSTRAINT chk_education_match_percentage 
        CHECK (education_match_percentage >= 0.00 AND education_match_percentage <= 100.00),
    CONSTRAINT chk_confidence_score 
        CHECK (confidence_score >= 0.00 AND confidence_score <= 1.00),
    CONSTRAINT chk_keyword_counts 
        CHECK (keyword_overlap_count >= 0 AND resume_keyword_count >= 0 AND jd_keyword_count >= 0),
    CONSTRAINT chk_keyword_overlap_logic 
        CHECK (keyword_overlap_count <= LEAST(resume_keyword_count, jd_keyword_count))
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_matching_scores_user_id ON matching_scores(user_id);
CREATE INDEX IF NOT EXISTS idx_matching_scores_resume_id ON matching_scores(resume_id);
CREATE INDEX IF NOT EXISTS idx_matching_scores_job_description_id ON matching_scores(job_description_id);
CREATE INDEX IF NOT EXISTS idx_matching_scores_overall_match ON matching_scores(overall_match_percentage);
CREATE INDEX IF NOT EXISTS idx_matching_scores_created_at ON matching_scores(created_at);
CREATE INDEX IF NOT EXISTS idx_matching_scores_is_current ON matching_scores(is_current);

-- Create composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_matching_scores_user_resume ON matching_scores(user_id, resume_id);
CREATE INDEX IF NOT EXISTS idx_matching_scores_user_jd ON matching_scores(user_id, job_description_id);
CREATE INDEX IF NOT EXISTS idx_matching_scores_resume_jd ON matching_scores(resume_id, job_description_id);
CREATE INDEX IF NOT EXISTS idx_matching_scores_current_user ON matching_scores(user_id, is_current) WHERE is_current = TRUE;

-- Create unique constraint to prevent duplicate current scores
CREATE UNIQUE INDEX IF NOT EXISTS idx_matching_scores_unique_current 
    ON matching_scores(resume_id, job_description_id) 
    WHERE is_current = TRUE;

-- Create trigger to automatically update updated_at timestamp
-- (Reuse the function created in US-01)
CREATE TRIGGER trigger_matching_scores_updated_at
    BEFORE UPDATE ON matching_scores
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create a view for matching score summary
CREATE OR REPLACE VIEW matching_scores_summary AS
SELECT 
    ms.id,
    ms.user_id,
    ms.resume_id,
    ms.job_description_id,
    ms.overall_match_percentage,
    ms.jaccard_similarity,
    ms.keyword_overlap_count,
    ms.resume_keyword_count,
    ms.jd_keyword_count,
    ms.skill_match_percentage,
    ms.experience_match_percentage,
    ms.education_match_percentage,
    ms.confidence_score,
    ms.is_current,
    ms.created_at,
    ms.last_calculated_at,
    u.email as user_email,
    u.first_name,
    u.last_name,
    r.resume_title,
    r.original_filename as resume_filename,
    jd.title as job_title,
    jd.company_name,
    CASE 
        WHEN ms.overall_match_percentage >= 80 THEN 'excellent'
        WHEN ms.overall_match_percentage >= 60 THEN 'good'
        WHEN ms.overall_match_percentage >= 40 THEN 'fair'
        WHEN ms.overall_match_percentage >= 20 THEN 'poor'
        ELSE 'very_poor'
    END as match_category
FROM matching_scores ms
JOIN users u ON ms.user_id = u.id
JOIN resumes r ON ms.resume_id = r.id
JOIN job_descriptions jd ON ms.job_description_id = jd.id
WHERE u.is_active = TRUE AND ms.is_current = TRUE;

-- Create a view for top matches by user
CREATE OR REPLACE VIEW top_matches_by_user AS
SELECT 
    user_id,
    COUNT(*) as total_matches,
    AVG(overall_match_percentage) as avg_match_percentage,
    MAX(overall_match_percentage) as best_match_percentage,
    MIN(overall_match_percentage) as worst_match_percentage,
    COUNT(CASE WHEN overall_match_percentage >= 80 THEN 1 END) as excellent_matches,
    COUNT(CASE WHEN overall_match_percentage >= 60 THEN 1 END) as good_matches,
    COUNT(CASE WHEN overall_match_percentage < 40 THEN 1 END) as poor_matches
FROM matching_scores
WHERE is_current = TRUE
GROUP BY user_id;

-- Create a function to calculate Jaccard similarity
CREATE OR REPLACE FUNCTION calculate_jaccard_similarity(
    keywords1 TEXT[],
    keywords2 TEXT[]
)
RETURNS DECIMAL(5,4) AS $$
DECLARE
    intersection_count INTEGER;
    union_count INTEGER;
    jaccard_score DECIMAL(5,4);
BEGIN
    -- Calculate intersection (common keywords)
    SELECT COUNT(*)
    INTO intersection_count
    FROM (
        SELECT UNNEST(keywords1)
        INTERSECT
        SELECT UNNEST(keywords2)
    ) AS intersection;
    
    -- Calculate union (all unique keywords)
    SELECT COUNT(*)
    INTO union_count
    FROM (
        SELECT UNNEST(keywords1)
        UNION
        SELECT UNNEST(keywords2)
    ) AS union_set;
    
    -- Calculate Jaccard similarity
    IF union_count = 0 THEN
        jaccard_score := 0.0000;
    ELSE
        jaccard_score := intersection_count::DECIMAL / union_count::DECIMAL;
    END IF;
    
    RETURN jaccard_score;
END;
$$ LANGUAGE plpgsql;

-- Create a function to get matching score for a resume-JD pair
CREATE OR REPLACE FUNCTION get_current_matching_score(
    p_user_id VARCHAR(36),
    p_resume_id VARCHAR(36),
    p_job_description_id VARCHAR(36)
)
RETURNS TABLE (
    overall_match_percentage DECIMAL(5,2),
    jaccard_similarity DECIMAL(5,4),
    keyword_overlap_count INTEGER,
    matched_keywords TEXT,
    missing_keywords TEXT,
    match_category TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ms.overall_match_percentage,
        ms.jaccard_similarity,
        ms.keyword_overlap_count,
        ms.matched_keywords,
        ms.missing_keywords,
        CASE 
            WHEN ms.overall_match_percentage >= 80 THEN 'excellent'
            WHEN ms.overall_match_percentage >= 60 THEN 'good'
            WHEN ms.overall_match_percentage >= 40 THEN 'fair'
            WHEN ms.overall_match_percentage >= 20 THEN 'poor'
            ELSE 'very_poor'
        END as match_category
    FROM matching_scores ms
    WHERE ms.user_id = p_user_id
        AND ms.resume_id = p_resume_id
        AND ms.job_description_id = p_job_description_id
        AND ms.is_current = TRUE;
END;
$$ LANGUAGE plpgsql;

-- Create a function to mark old scores as outdated
CREATE OR REPLACE FUNCTION mark_scores_for_recalculation(
    p_resume_id VARCHAR(36) DEFAULT NULL,
    p_job_description_id VARCHAR(36) DEFAULT NULL
)
RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER;
BEGIN
    IF p_resume_id IS NOT NULL THEN
        UPDATE matching_scores 
        SET needs_recalculation = TRUE,
            updated_at = CURRENT_TIMESTAMP
        WHERE resume_id = p_resume_id AND is_current = TRUE;
        GET DIAGNOSTICS updated_count = ROW_COUNT;
        RETURN updated_count;
    END IF;
    
    IF p_job_description_id IS NOT NULL THEN
        UPDATE matching_scores 
        SET needs_recalculation = TRUE,
            updated_at = CURRENT_TIMESTAMP
        WHERE job_description_id = p_job_description_id AND is_current = TRUE;
        GET DIAGNOSTICS updated_count = ROW_COUNT;
        RETURN updated_count;
    END IF;
    
    RETURN 0;
END;
$$ LANGUAGE plpgsql;

-- Insert sample data for testing (optional)
-- Uncomment the following lines if you want sample data

/*
-- First, get sample IDs from existing data
DO $$
DECLARE
    sample_user_id VARCHAR(36);
    sample_resume_id VARCHAR(36);
    sample_jd_id VARCHAR(36);
BEGIN
    -- Get sample user
    SELECT id INTO sample_user_id FROM users WHERE email = '<EMAIL>' LIMIT 1;
    
    IF sample_user_id IS NOT NULL THEN
        -- Get sample resume
        SELECT id INTO sample_resume_id FROM resumes WHERE user_id = sample_user_id LIMIT 1;
        
        -- Get sample job description
        SELECT id INTO sample_jd_id FROM job_descriptions WHERE user_id = sample_user_id LIMIT 1;
        
        -- Insert sample matching score
        IF sample_resume_id IS NOT NULL AND sample_jd_id IS NOT NULL THEN
            INSERT INTO matching_scores (
                user_id, resume_id, job_description_id, 
                overall_match_percentage, jaccard_similarity,
                keyword_overlap_count, resume_keyword_count, jd_keyword_count,
                skill_match_percentage, experience_match_percentage, education_match_percentage,
                matched_keywords, missing_keywords, extra_keywords
            ) VALUES (
                sample_user_id, sample_resume_id, sample_jd_id,
                75.50, 0.6250,
                5, 8, 10,
                80.00, 70.00, 75.00,
                '["python", "flask", "postgresql", "software engineer", "bachelor degree"]',
                '["react", "docker", "aws", "senior level", "computer science"]',
                '["javascript", "html", "css"]'
            );
        END IF;
    END IF;
END $$;
*/

-- Verify table creation
SELECT 
    table_name, 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'matching_scores' 
ORDER BY ordinal_position;

-- Display table structure
\d matching_scores;

-- Show indexes
\di matching_scores*;

-- Show foreign key constraints
SELECT
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_name='matching_scores';

-- Show view definitions
\d+ matching_scores_summary;
\d+ top_matches_by_user;

-- Test queries to verify relationships work
-- SELECT COUNT(*) as total_matching_scores FROM matching_scores;
-- SELECT match_category, COUNT(*) as count FROM matching_scores_summary GROUP BY match_category;
-- SELECT * FROM top_matches_by_user LIMIT 5;
