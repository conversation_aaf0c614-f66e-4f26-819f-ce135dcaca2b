<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dr. Resume - Matching Score (US-06)</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            color: white !important;
        }
        
        .nav-link {
            color: rgba(255,255,255,0.9) !important;
        }
        
        .nav-link:hover {
            color: white !important;
        }
        
        .matching-container {
            padding: 30px 0;
        }
        
        .matching-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
            margin-bottom: 20px;
        }
        
        /* Progress Bar Styles (as per requirements) */
        .progress-container {
            margin: 20px 0;
        }
        
        .progress {
            height: 25px;
            border-radius: 12px;
            background-color: #e9ecef;
            overflow: hidden;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.2);
        }
        
        .progress-bar {
            border-radius: 12px;
            font-weight: 600;
            font-size: 14px;
            line-height: 25px;
            transition: all 0.6s ease;
            position: relative;
        }
        
        .progress-bar.excellent {
            background: linear-gradient(45deg, #28a745, #20c997);
        }
        
        .progress-bar.good {
            background: linear-gradient(45deg, #17a2b8, #6f42c1);
        }
        
        .progress-bar.fair {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
        }
        
        .progress-bar.poor {
            background: linear-gradient(45deg, #fd7e14, #dc3545);
        }
        
        .progress-bar.very-poor {
            background: linear-gradient(45deg, #dc3545, #6c757d);
        }
        
        /* Color-coded indicators (as per requirements) */
        .match-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .match-indicator.excellent { background-color: #28a745; }
        .match-indicator.good { background-color: #17a2b8; }
        .match-indicator.fair { background-color: #ffc107; }
        .match-indicator.poor { background-color: #fd7e14; }
        .match-indicator.very-poor { background-color: #dc3545; }
        
        .form-control {
            border-radius: 8px;
            border: 1px solid #dee2e6;
            padding: 10px 15px;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 10px 25px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .feature-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 20px;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            padding: 15px 20px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
        }
        
        .alert-danger {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
        }
        
        .alert-info {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            color: #0c5460;
        }
        
        .loading-spinner {
            display: none;
        }
        
        .loading-spinner.show {
            display: inline-block;
        }
        
        .match-details {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .keyword-list {
            max-height: 200px;
            overflow-y: auto;
            background: white;
            border-radius: 5px;
            padding: 10px;
            border: 1px solid #dee2e6;
        }
        
        .keyword-tag {
            display: inline-block;
            background: #e9ecef;
            color: #495057;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin: 2px;
        }
        
        .keyword-tag.matched {
            background: #d4edda;
            color: #155724;
        }
        
        .keyword-tag.missing {
            background: #f8d7da;
            color: #721c24;
        }
        
        .keyword-tag.extra {
            background: #fff3cd;
            color: #856404;
        }
        
        .category-breakdown {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .category-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            text-align: center;
        }
        
        .category-score {
            font-size: 24px;
            font-weight: 700;
            margin: 10px 0;
        }
        
        .help-text {
            font-size: 13px;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-chart-line me-2"></i>
                Dr. Resume
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="goToRegistration()">
                            <i class="fas fa-user-plus me-1"></i>Register
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="goToLogin()">
                            <i class="fas fa-sign-in-alt me-1"></i>Login
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="goToResumeUpload()">
                            <i class="fas fa-upload me-1"></i>Upload Resume
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="goToJDUpload()">
                            <i class="fas fa-briefcase me-1"></i>Upload Job Description
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="#">
                            <i class="fas fa-chart-line me-1"></i>Matching Score
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="logout()">
                            <i class="fas fa-sign-out-alt me-1"></i>Logout
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container matching-container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <!-- Feature Badge -->
                <div class="text-center">
                    <span class="feature-badge">US-06: Matching Score with Progress Bars</span>
                </div>
                
                <!-- Header -->
                <div class="matching-card">
                    <div class="text-center mb-4">
                        <h2 class="mb-3">
                            <i class="fas fa-chart-line text-primary me-2"></i>
                            Resume-Job Description Matching
                        </h2>
                        <p class="text-muted">
                            Calculate matching scores using Jaccard similarity and view results with color-coded progress bars
                        </p>
                    </div>
                    
                    <!-- Alert Messages -->
                    <div id="alertContainer"></div>
                    
                    <!-- Calculation Form -->
                    <form id="matchingForm">
                        <div class="row">
                            <!-- Resume Selection -->
                            <div class="col-md-6 mb-3">
                                <label for="resumeSelect" class="form-label">
                                    <i class="fas fa-file-alt me-1"></i>Select Resume
                                </label>
                                <select class="form-control" id="resumeSelect" name="resume_id" required>
                                    <option value="">Loading resumes...</option>
                                </select>
                                <div class="help-text">Choose a resume to compare</div>
                            </div>
                            
                            <!-- Job Description Selection -->
                            <div class="col-md-6 mb-3">
                                <label for="jdSelect" class="form-label">
                                    <i class="fas fa-briefcase me-1"></i>Select Job Description
                                </label>
                                <select class="form-control" id="jdSelect" name="job_description_id" required>
                                    <option value="">Loading job descriptions...</option>
                                </select>
                                <div class="help-text">Choose a job description to compare against</div>
                            </div>
                        </div>
                        
                        <!-- Calculation Method -->
                        <div class="mb-3">
                            <label for="methodSelect" class="form-label">
                                <i class="fas fa-calculator me-1"></i>Calculation Method
                            </label>
                            <select class="form-control" id="methodSelect" name="calculation_method">
                                <option value="jaccard">Jaccard Similarity (Default)</option>
                                <option value="weighted">Weighted Similarity</option>
                                <option value="hybrid">Hybrid Method</option>
                            </select>
                            <div class="help-text">
                                Jaccard: Simple keyword overlap | Weighted: Considers keyword importance | Hybrid: Combines both methods
                            </div>
                        </div>
                        
                        <!-- Calculate Button -->
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary" id="calculateBtn">
                                <span class="loading-spinner spinner-border spinner-border-sm me-2" id="loadingSpinner"></span>
                                <i class="fas fa-calculator me-2"></i>
                                Calculate Matching Score
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- Matching Results (Progress Bars as per requirements) -->
                <div class="matching-card" id="resultsContainer" style="display: none;">
                    <h4 class="mb-4">
                        <i class="fas fa-chart-bar text-primary me-2"></i>
                        Matching Results
                    </h4>
                    
                    <!-- Overall Match Progress Bar -->
                    <div class="progress-container">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-bold">
                                <span class="match-indicator" id="overallIndicator"></span>
                                Overall Match
                            </span>
                            <span class="fw-bold" id="overallPercentage">0%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" id="overallProgressBar" role="progressbar" style="width: 0%">
                                <span id="overallProgressText">0%</span>
                            </div>
                        </div>
                        <div class="help-text mt-1" id="overallCategory">Calculating...</div>
                    </div>
                    
                    <!-- Category Breakdown Progress Bars -->
                    <div class="category-breakdown" id="categoryBreakdown">
                        <!-- Skills Match -->
                        <div class="category-item">
                            <div class="fw-bold mb-2">
                                <i class="fas fa-cogs text-primary me-1"></i>Skills
                            </div>
                            <div class="progress mb-2" style="height: 20px;">
                                <div class="progress-bar" id="skillsProgressBar" role="progressbar" style="width: 0%">
                                    <span id="skillsProgressText">0%</span>
                                </div>
                            </div>
                            <div class="category-score" id="skillsScore">0%</div>
                        </div>
                        
                        <!-- Experience Match -->
                        <div class="category-item">
                            <div class="fw-bold mb-2">
                                <i class="fas fa-briefcase text-primary me-1"></i>Experience
                            </div>
                            <div class="progress mb-2" style="height: 20px;">
                                <div class="progress-bar" id="experienceProgressBar" role="progressbar" style="width: 0%">
                                    <span id="experienceProgressText">0%</span>
                                </div>
                            </div>
                            <div class="category-score" id="experienceScore">0%</div>
                        </div>
                        
                        <!-- Education Match -->
                        <div class="category-item">
                            <div class="fw-bold mb-2">
                                <i class="fas fa-graduation-cap text-primary me-1"></i>Education
                            </div>
                            <div class="progress mb-2" style="height: 20px;">
                                <div class="progress-bar" id="educationProgressBar" role="progressbar" style="width: 0%">
                                    <span id="educationProgressText">0%</span>
                                </div>
                            </div>
                            <div class="category-score" id="educationScore">0%</div>
                        </div>
                    </div>
                    
                    <!-- Detailed Analysis -->
                    <div class="match-details" id="matchDetails">
                        <h5 class="mb-3">
                            <i class="fas fa-search text-primary me-2"></i>
                            Detailed Analysis
                        </h5>
                        
                        <!-- Keyword Analysis Tabs -->
                        <ul class="nav nav-tabs" id="keywordTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="matched-tab" data-bs-toggle="tab" data-bs-target="#matched" type="button" role="tab">
                                    <span class="keyword-tag matched">Matched Keywords</span>
                                    <span class="badge bg-success ms-1" id="matchedCount">0</span>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="missing-tab" data-bs-toggle="tab" data-bs-target="#missing" type="button" role="tab">
                                    <span class="keyword-tag missing">Missing Keywords</span>
                                    <span class="badge bg-danger ms-1" id="missingCount">0</span>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="extra-tab" data-bs-toggle="tab" data-bs-target="#extra" type="button" role="tab">
                                    <span class="keyword-tag extra">Extra Keywords</span>
                                    <span class="badge bg-warning ms-1" id="extraCount">0</span>
                                </button>
                            </li>
                        </ul>
                        
                        <div class="tab-content mt-3" id="keywordTabContent">
                            <div class="tab-pane fade show active" id="matched" role="tabpanel">
                                <div class="keyword-list" id="matchedKeywords">
                                    <p class="text-muted">No matched keywords to display</p>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="missing" role="tabpanel">
                                <div class="keyword-list" id="missingKeywords">
                                    <p class="text-muted">No missing keywords to display</p>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="extra" role="tabpanel">
                                <div class="keyword-list" id="extraKeywords">
                                    <p class="text-muted">No extra keywords to display</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Calculation Metadata -->
                        <div class="mt-3 pt-3 border-top">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                <span id="calculationInfo">Calculation details will appear here</span>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="us06_matching.js"></script>
</body>
</html>
