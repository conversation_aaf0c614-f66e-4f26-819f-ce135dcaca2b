/**
 * US-06: Matching Score - Frontend JavaScript
 * ==========================================
 * 
 * This file handles the frontend logic for matching score calculation and display.
 * It manages form submission, API calls, and progress bar visualization.
 * 
 * Tech Stack: Vanilla JavaScript, Bootstrap, Fetch API
 * Dependencies: US-02 (JWT authentication)
 */

// Configuration
const API_BASE_URL = 'http://localhost:5000/api';

// DOM Elements
let matchingForm;
let calculateBtn;
let loadingSpinner;
let alertContainer;
let resumeSelect;
let jdSelect;
let methodSelect;
let resultsContainer;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeElements();
    setupEventListeners();
    checkAuthentication();
    loadDocuments();
});

/**
 * Initialize DOM elements
 */
function initializeElements() {
    matchingForm = document.getElementById('matchingForm');
    calculateBtn = document.getElementById('calculateBtn');
    loadingSpinner = document.getElementById('loadingSpinner');
    alertContainer = document.getElementById('alertContainer');
    resumeSelect = document.getElementById('resumeSelect');
    jdSelect = document.getElementById('jdSelect');
    methodSelect = document.getElementById('methodSelect');
    resultsContainer = document.getElementById('resultsContainer');
}

/**
 * Setup event listeners
 */
function setupEventListeners() {
    // Form submission
    if (matchingForm) {
        matchingForm.addEventListener('submit', handleFormSubmit);
    }
}

/**
 * Check if user is authenticated
 */
function checkAuthentication() {
    const token = localStorage.getItem('access_token');
    
    if (!token) {
        showAlert('warning', 'Please log in to calculate matching scores.', true);
        setTimeout(() => {
            goToLogin();
        }, 3000);
        return false;
    }
    
    return true;
}

/**
 * Load available documents (resumes and job descriptions)
 */
async function loadDocuments() {
    if (!checkAuthentication()) {
        return;
    }
    
    try {
        const token = localStorage.getItem('access_token');
        
        // Load resumes
        const resumeResponse = await fetch(`${API_BASE_URL}/resumes?summary=true`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (resumeResponse.ok) {
            const resumeData = await resumeResponse.json();
            populateResumeSelect(resumeData.resumes || []);
        } else {
            console.error('Failed to load resumes');
            resumeSelect.innerHTML = '<option value="">No resumes available</option>';
        }
        
        // Load job descriptions
        const jdResponse = await fetch(`${API_BASE_URL}/job_descriptions?summary=true`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (jdResponse.ok) {
            const jdData = await jdResponse.json();
            populateJDSelect(jdData.job_descriptions || []);
        } else {
            console.error('Failed to load job descriptions');
            jdSelect.innerHTML = '<option value="">No job descriptions available</option>';
        }
        
    } catch (error) {
        console.error('Error loading documents:', error);
        showAlert('danger', 'Error loading documents. Please refresh the page.');
    }
}

/**
 * Populate resume select dropdown
 */
function populateResumeSelect(resumes) {
    resumeSelect.innerHTML = '<option value="">Select a resume...</option>';
    
    resumes.forEach(resume => {
        if (resume.keywords_extracted) {  // Only show processed resumes
            const option = document.createElement('option');
            option.value = resume.id;
            option.textContent = resume.resume_title || resume.original_filename || 'Untitled Resume';
            resumeSelect.appendChild(option);
        }
    });
    
    if (resumeSelect.children.length === 1) {
        resumeSelect.innerHTML = '<option value="">No processed resumes available</option>';
        showAlert('info', 'No processed resumes found. Please upload and process a resume first.');
    }
}

/**
 * Populate job description select dropdown
 */
function populateJDSelect(jobDescriptions) {
    jdSelect.innerHTML = '<option value="">Select a job description...</option>';
    
    jobDescriptions.forEach(jd => {
        if (jd.keywords_extracted) {  // Only show processed JDs
            const option = document.createElement('option');
            option.value = jd.id;
            option.textContent = `${jd.title}${jd.company_name ? ' at ' + jd.company_name : ''}`;
            jdSelect.appendChild(option);
        }
    });
    
    if (jdSelect.children.length === 1) {
        jdSelect.innerHTML = '<option value="">No processed job descriptions available</option>';
        showAlert('info', 'No processed job descriptions found. Please upload and process a job description first.');
    }
}

/**
 * Handle form submission
 */
async function handleFormSubmit(event) {
    event.preventDefault();
    
    if (!checkAuthentication()) {
        return;
    }
    
    // Validate form
    if (!validateForm()) {
        return;
    }
    
    // Show loading state
    setLoadingState(true);
    
    try {
        // Prepare form data
        const formData = new FormData(matchingForm);
        const matchingData = {
            resume_id: formData.get('resume_id'),
            job_description_id: formData.get('job_description_id'),
            calculation_method: formData.get('calculation_method')
        };
        
        // Make API call
        const response = await fetch(`${API_BASE_URL}/calculate_match`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            },
            body: JSON.stringify(matchingData)
        });
        
        const result = await response.json();
        
        if (response.ok && result.success) {
            // Success - display results
            displayMatchingResults(result.matching_score, result.calculation_details);
            showAlert('success', result.message);
        } else {
            // Error from API
            showAlert('danger', result.message || 'Failed to calculate matching score');
        }
        
    } catch (error) {
        console.error('Calculation error:', error);
        
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            showAlert('danger', 'Unable to connect to server. Please check your connection and try again.');
        } else {
            showAlert('danger', 'An unexpected error occurred. Please try again.');
        }
    } finally {
        setLoadingState(false);
    }
}

/**
 * Validate the form
 */
function validateForm() {
    clearAlerts();
    
    const resumeId = resumeSelect.value;
    const jdId = jdSelect.value;
    
    if (!resumeId) {
        showAlert('danger', 'Please select a resume');
        return false;
    }
    
    if (!jdId) {
        showAlert('danger', 'Please select a job description');
        return false;
    }
    
    return true;
}

/**
 * Display matching results with progress bars (as per requirements)
 */
function displayMatchingResults(matchingScore, calculationDetails) {
    // Show results container
    resultsContainer.style.display = 'block';
    
    // Scroll to results
    resultsContainer.scrollIntoView({ behavior: 'smooth' });
    
    // Update overall match progress bar
    updateProgressBar(
        'overall',
        matchingScore.overall_match_percentage,
        matchingScore.match_category
    );
    
    // Update category progress bars
    updateProgressBar('skills', matchingScore.skill_match_percentage, getScoreCategory(matchingScore.skill_match_percentage));
    updateProgressBar('experience', matchingScore.experience_match_percentage, getScoreCategory(matchingScore.experience_match_percentage));
    updateProgressBar('education', matchingScore.education_match_percentage, getScoreCategory(matchingScore.education_match_percentage));
    
    // Update keyword analysis
    updateKeywordAnalysis(matchingScore);
    
    // Update calculation info
    updateCalculationInfo(matchingScore, calculationDetails);
}

/**
 * Update progress bar with color-coded indicators (as per requirements)
 */
function updateProgressBar(type, percentage, category) {
    const progressBar = document.getElementById(`${type}ProgressBar`);
    const progressText = document.getElementById(`${type}ProgressText`);
    const percentageElement = document.getElementById(`${type}Percentage`);
    const scoreElement = document.getElementById(`${type}Score`);
    const indicator = document.getElementById(`${type}Indicator`);
    const categoryElement = document.getElementById(`${type}Category`);
    
    // Update progress bar
    if (progressBar) {
        progressBar.style.width = `${percentage}%`;
        progressBar.className = `progress-bar ${category}`;
        progressBar.setAttribute('aria-valuenow', percentage);
    }
    
    // Update text
    if (progressText) {
        progressText.textContent = `${percentage}%`;
    }
    
    if (percentageElement) {
        percentageElement.textContent = `${percentage}%`;
    }
    
    if (scoreElement) {
        scoreElement.textContent = `${percentage}%`;
    }
    
    // Update color indicator
    if (indicator) {
        indicator.className = `match-indicator ${category}`;
    }
    
    // Update category text
    if (categoryElement) {
        const categoryText = {
            'excellent': 'Excellent Match (80%+)',
            'good': 'Good Match (60-79%)',
            'fair': 'Fair Match (40-59%)',
            'poor': 'Poor Match (20-39%)',
            'very-poor': 'Very Poor Match (<20%)'
        };
        categoryElement.textContent = categoryText[category] || 'Unknown';
    }
}

/**
 * Get score category based on percentage
 */
function getScoreCategory(percentage) {
    if (percentage >= 80) return 'excellent';
    if (percentage >= 60) return 'good';
    if (percentage >= 40) return 'fair';
    if (percentage >= 20) return 'poor';
    return 'very-poor';
}

/**
 * Update keyword analysis tabs
 */
function updateKeywordAnalysis(matchingScore) {
    // Update counts
    document.getElementById('matchedCount').textContent = matchingScore.matched_keywords.length;
    document.getElementById('missingCount').textContent = matchingScore.missing_keywords.length;
    document.getElementById('extraCount').textContent = matchingScore.extra_keywords.length;
    
    // Update keyword lists
    updateKeywordList('matchedKeywords', matchingScore.matched_keywords, 'matched');
    updateKeywordList('missingKeywords', matchingScore.missing_keywords, 'missing');
    updateKeywordList('extraKeywords', matchingScore.extra_keywords, 'extra');
}

/**
 * Update keyword list
 */
function updateKeywordList(containerId, keywords, type) {
    const container = document.getElementById(containerId);
    
    if (keywords.length === 0) {
        container.innerHTML = `<p class="text-muted">No ${type} keywords to display</p>`;
        return;
    }
    
    container.innerHTML = keywords.map(keyword => 
        `<span class="keyword-tag ${type}">${keyword}</span>`
    ).join('');
}

/**
 * Update calculation information
 */
function updateCalculationInfo(matchingScore, calculationDetails) {
    const infoElement = document.getElementById('calculationInfo');
    
    const info = [
        `Method: ${calculationDetails.method}`,
        `Jaccard Similarity: ${(matchingScore.jaccard_similarity * 100).toFixed(2)}%`,
        `Processing Time: ${calculationDetails.processing_time_ms}ms`,
        `Algorithm: v${calculationDetails.algorithm_version}`
    ].join(' | ');
    
    infoElement.textContent = info;
}

/**
 * Set loading state
 */
function setLoadingState(isLoading) {
    if (isLoading) {
        calculateBtn.disabled = true;
        loadingSpinner.classList.add('show');
        calculateBtn.innerHTML = `
            <span class="loading-spinner spinner-border spinner-border-sm me-2 show"></span>
            <i class="fas fa-calculator me-2"></i>
            Calculating...
        `;
    } else {
        calculateBtn.disabled = false;
        loadingSpinner.classList.remove('show');
        calculateBtn.innerHTML = `
            <span class="loading-spinner spinner-border spinner-border-sm me-2"></span>
            <i class="fas fa-calculator me-2"></i>
            Calculate Matching Score
        `;
    }
}

/**
 * Show alert message
 */
function showAlert(type, message, persistent = false) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.appendChild(alertDiv);
    
    // Auto-dismiss after 5 seconds unless persistent
    if (!persistent) {
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}

/**
 * Clear all alerts
 */
function clearAlerts() {
    alertContainer.innerHTML = '';
}

// Navigation functions (to integrate with other US features)
function goToRegistration() {
    window.location.href = '../../US-01-User-Registration/frontend/us01_register.html';
}

function goToLogin() {
    window.location.href = '../../US-02-Login-JWT-Token/frontend/us02_login.html';
}

function goToResumeUpload() {
    window.location.href = '../../US-03-Resume-Upload/frontend/us03_upload.html';
}

function goToJDUpload() {
    window.location.href = '../../US-04-JD-Upload/frontend/us04_upload.html';
}

function logout() {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    showAlert('info', 'Logged out successfully');
    setTimeout(() => {
        goToLogin();
    }, 1000);
}
