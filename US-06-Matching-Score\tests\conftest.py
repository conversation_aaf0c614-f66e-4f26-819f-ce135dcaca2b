"""
US-06: Matching Score - Test Configuration
==========================================

This file contains pytest configuration and shared fixtures for US-06 tests.
It sets up the test environment and provides common test utilities.

Tech Stack: pytest, Flask-Testing
"""

import pytest
import sys
import os
from datetime import datetime

# Add backend directories to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-01-User-Registration', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-02-Login-JWT-Token', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-03-Resume-Upload', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-04-JD-Upload', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-05-Keyword-Parsing', 'backend'))

# Test configuration
pytest_plugins = []

def pytest_configure(config):
    """Configure pytest settings"""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "unit: marks tests as unit tests"
    )
    config.addinivalue_line(
        "markers", "matching: marks tests as matching calculation tests"
    )

@pytest.fixture(scope="session")
def test_config():
    """Test configuration settings"""
    return {
        'TESTING': True,
        'SQLALCHEMY_DATABASE_URI': 'sqlite:///:memory:',
        'JWT_SECRET_KEY': 'test-secret-key-for-us06',
        'SECRET_KEY': 'test-secret-key',
        'WTF_CSRF_ENABLED': False,
        'SQLALCHEMY_TRACK_MODIFICATIONS': False
    }

@pytest.fixture
def sample_matching_data():
    """Sample matching score data for testing"""
    return {
        'overall_match_percentage': 75.50,
        'jaccard_similarity': 0.6250,
        'keyword_overlap_count': 5,
        'resume_keyword_count': 8,
        'jd_keyword_count': 10,
        'skill_match_percentage': 80.00,
        'experience_match_percentage': 70.00,
        'education_match_percentage': 75.00,
        'matched_keywords': ['python', 'flask', 'postgresql', 'software engineer', 'bachelor degree'],
        'missing_keywords': ['react', 'docker', 'aws', 'senior level', 'computer science'],
        'extra_keywords': ['javascript', 'html', 'css'],
        'algorithm_version': '1.0',
        'calculation_method': 'jaccard',
        'confidence_score': 0.85
    }

@pytest.fixture
def sample_keyword_sets():
    """Sample keyword sets for similarity testing"""
    return {
        'resume_keywords': [
            {'keyword': 'python', 'keyword_type': 'technology', 'frequency': 3, 'confidence_score': 0.95},
            {'keyword': 'flask', 'keyword_type': 'framework', 'frequency': 2, 'confidence_score': 0.90},
            {'keyword': 'postgresql', 'keyword_type': 'technology', 'frequency': 1, 'confidence_score': 0.85},
            {'keyword': 'software engineer', 'keyword_type': 'experience', 'frequency': 1, 'confidence_score': 0.92},
            {'keyword': 'bachelor degree', 'keyword_type': 'education', 'frequency': 1, 'confidence_score': 0.88}
        ],
        'jd_keywords': [
            {'keyword': 'python', 'keyword_type': 'technology', 'frequency': 5, 'confidence_score': 0.98},
            {'keyword': 'flask', 'keyword_type': 'framework', 'frequency': 3, 'confidence_score': 0.95},
            {'keyword': 'react', 'keyword_type': 'framework', 'frequency': 2, 'confidence_score': 0.90},
            {'keyword': 'senior developer', 'keyword_type': 'experience', 'frequency': 2, 'confidence_score': 0.93},
            {'keyword': 'computer science', 'keyword_type': 'education', 'frequency': 1, 'confidence_score': 0.89},
            {'keyword': 'docker', 'keyword_type': 'tool', 'frequency': 1, 'confidence_score': 0.87}
        ]
    }

@pytest.fixture
def sample_user_data():
    """Sample user data for testing"""
    return {
        'email': '<EMAIL>',
        'password': 'password123',
        'first_name': 'Test',
        'last_name': 'User'
    }

@pytest.fixture
def sample_resume_data():
    """Sample resume data for testing"""
    return {
        'original_filename': 'test_resume.pdf',
        'extracted_text': 'Python developer with 5 years experience in Flask and PostgreSQL. Bachelor degree in Computer Science.',
        'keywords_extracted': True
    }

@pytest.fixture
def sample_jd_data():
    """Sample job description data for testing"""
    return {
        'title': 'Senior Python Developer',
        'company_name': 'TechCorp Solutions',
        'job_description_text': 'Looking for a Senior Python Developer with Flask experience and PostgreSQL knowledge. React experience is a plus. Docker knowledge preferred.',
        'keywords_extracted': True
    }

# Test utilities
class TestUtils:
    """Utility functions for testing"""
    
    @staticmethod
    def create_test_user(app, db, User, email='<EMAIL>'):
        """Create a test user"""
        with app.app_context():
            user = User(
                email=email,
                password='password123',
                first_name='Test',
                last_name='User'
            )
            db.session.add(user)
            db.session.commit()
            return user
    
    @staticmethod
    def create_test_resume(app, db, Resume, user_id, **kwargs):
        """Create a test resume"""
        with app.app_context():
            resume_data = {
                'user_id': user_id,
                'original_filename': 'test_resume.pdf',
                'extracted_text': 'Python developer with Flask experience',
                'keywords_extracted': True
            }
            resume_data.update(kwargs)
            
            resume = Resume(**resume_data)
            db.session.add(resume)
            db.session.commit()
            return resume
    
    @staticmethod
    def create_test_jd(app, db, JobDescription, user_id, **kwargs):
        """Create a test job description"""
        with app.app_context():
            jd_data = {
                'user_id': user_id,
                'title': 'Python Developer',
                'job_description_text': 'Looking for Python developer with Flask experience',
                'keywords_extracted': True
            }
            jd_data.update(kwargs)
            
            jd = JobDescription(**jd_data)
            db.session.add(jd)
            db.session.commit()
            return jd
    
    @staticmethod
    def create_test_keywords(app, db, Keyword, user_id, resume_id=None, jd_id=None, keywords_data=None):
        """Create test keywords"""
        with app.app_context():
            if keywords_data is None:
                keywords_data = [
                    {'keyword': 'python', 'keyword_type': 'technology'},
                    {'keyword': 'flask', 'keyword_type': 'framework'}
                ]
            
            created_keywords = []
            for kw_data in keywords_data:
                keyword = Keyword(
                    user_id=user_id,
                    resume_id=resume_id,
                    job_description_id=jd_id,
                    **kw_data
                )
                db.session.add(keyword)
                created_keywords.append(keyword)
            
            db.session.commit()
            return created_keywords
    
    @staticmethod
    def create_test_matching_score(app, db, MatchingScore, user_id, resume_id, jd_id, **kwargs):
        """Create a test matching score"""
        with app.app_context():
            score_data = {
                'user_id': user_id,
                'resume_id': resume_id,
                'job_description_id': jd_id,
                'overall_match_percentage': 75.50,
                'jaccard_similarity': 0.6250,
                'keyword_overlap_count': 3
            }
            score_data.update(kwargs)
            
            matching_score = MatchingScore(**score_data)
            db.session.add(matching_score)
            db.session.commit()
            return matching_score
    
    @staticmethod
    def authenticate_user(client, email='<EMAIL>', password='password123'):
        """Authenticate a user and return access token"""
        import json
        
        login_data = {
            'email': email,
            'password': password
        }
        
        response = client.post('/api/login',
                             data=json.dumps(login_data),
                             content_type='application/json')
        
        if response.status_code == 200:
            data = json.loads(response.data)
            return data['tokens']['access_token']
        return None
    
    @staticmethod
    def create_auth_headers(token):
        """Create authorization headers"""
        return {'Authorization': f'Bearer {token}'}
    
    @staticmethod
    def assert_matching_score_structure(score_dict):
        """Assert that matching score dictionary has correct structure"""
        required_fields = [
            'id', 'user_id', 'resume_id', 'job_description_id',
            'overall_match_percentage', 'jaccard_similarity',
            'keyword_overlap_count', 'resume_keyword_count', 'jd_keyword_count',
            'matched_keywords', 'missing_keywords', 'extra_keywords',
            'match_category', 'is_current', 'created_at'
        ]
        
        for field in required_fields:
            assert field in score_dict, f"Missing required field: {field}"
        
        # Check data types
        assert isinstance(score_dict['overall_match_percentage'], (int, float))
        assert isinstance(score_dict['jaccard_similarity'], (int, float))
        assert isinstance(score_dict['keyword_overlap_count'], int)
        assert isinstance(score_dict['matched_keywords'], list)
        assert isinstance(score_dict['missing_keywords'], list)
        assert isinstance(score_dict['extra_keywords'], list)
        assert isinstance(score_dict['is_current'], bool)
        
        # Check value ranges
        assert 0 <= score_dict['overall_match_percentage'] <= 100
        assert 0 <= score_dict['jaccard_similarity'] <= 1
        assert score_dict['keyword_overlap_count'] >= 0
        
        # Check match category
        valid_categories = ['excellent', 'good', 'fair', 'poor', 'very_poor']
        assert score_dict['match_category'] in valid_categories
    
    @staticmethod
    def calculate_expected_jaccard(set1, set2):
        """Calculate expected Jaccard similarity for testing"""
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        return intersection / union if union > 0 else 0.0

# Make TestUtils available as a fixture
@pytest.fixture
def test_utils():
    """Test utilities fixture"""
    return TestUtils
