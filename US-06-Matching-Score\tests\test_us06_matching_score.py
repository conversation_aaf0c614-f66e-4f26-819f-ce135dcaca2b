"""
US-06: Matching Score - Test Suite
=================================

This file contains comprehensive tests for US-06 Matching Score functionality.
It tests the matching model, calculation logic, and API endpoints.

Tech Stack: pytest, Flask-Testing, SQLAlchemy
Dependencies: US-01 (User model), US-02 (JWT authentication), US-03 (Resume model), 
              US-04 (JobDescription model), US-05 (Keyword model)
"""

import pytest
import json
import sys
import os
from datetime import datetime

# Add backend directories to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-01-User-Registration', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-02-Login-JWT-Token', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-03-Resume-Upload', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-04-JD-Upload', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'US-05-Keyword-Parsing', 'backend'))

from us06_app import create_app
from us01_user_model import db, User
from us03_resume_model import Resume
from us04_jd_model import JobDescription
from us05_keyword_model import Keyword
from us06_matching_model import MatchingScore
from us06_matching_calculator import MatchingCalculator, get_matching_calculator

class TestMatchingScoreModel:
    """Test cases for MatchingScore model functionality"""
    
    @pytest.fixture
    def app(self):
        """Create test Flask application"""
        app = create_app()
        app.config['TESTING'] = True
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        app.config['JWT_SECRET_KEY'] = 'test-secret-key'
        
        with app.app_context():
            db.create_all()
            yield app
            db.drop_all()
    
    @pytest.fixture
    def client(self, app):
        """Create test client"""
        return app.test_client()
    
    @pytest.fixture
    def sample_user(self, app):
        """Create a sample user for testing"""
        with app.app_context():
            user = User(
                email='<EMAIL>',
                password='password123',
                first_name='Test',
                last_name='User'
            )
            db.session.add(user)
            db.session.commit()
            return user
    
    @pytest.fixture
    def sample_resume(self, app, sample_user):
        """Create a sample resume for testing"""
        with app.app_context():
            resume = Resume(
                user_id=sample_user.id,
                original_filename='test_resume.pdf',
                extracted_text='Python developer with Flask experience',
                keywords_extracted=True
            )
            db.session.add(resume)
            db.session.commit()
            return resume
    
    @pytest.fixture
    def sample_jd(self, app, sample_user):
        """Create a sample job description for testing"""
        with app.app_context():
            jd = JobDescription(
                user_id=sample_user.id,
                title='Senior Python Developer',
                job_description_text='Looking for Python developer with Flask experience',
                keywords_extracted=True
            )
            db.session.add(jd)
            db.session.commit()
            return jd
    
    @pytest.fixture
    def sample_keywords(self, app, sample_user, sample_resume, sample_jd):
        """Create sample keywords for testing"""
        with app.app_context():
            # Resume keywords
            resume_keywords = [
                {'keyword': 'python', 'keyword_type': 'technology'},
                {'keyword': 'flask', 'keyword_type': 'framework'},
                {'keyword': 'developer', 'keyword_type': 'experience'}
            ]
            
            for kw_data in resume_keywords:
                keyword = Keyword(
                    user_id=sample_user.id,
                    resume_id=sample_resume.id,
                    **kw_data
                )
                db.session.add(keyword)
            
            # JD keywords
            jd_keywords = [
                {'keyword': 'python', 'keyword_type': 'technology'},
                {'keyword': 'flask', 'keyword_type': 'framework'},
                {'keyword': 'senior', 'keyword_type': 'experience'},
                {'keyword': 'react', 'keyword_type': 'framework'}
            ]
            
            for kw_data in jd_keywords:
                keyword = Keyword(
                    user_id=sample_user.id,
                    job_description_id=sample_jd.id,
                    **kw_data
                )
                db.session.add(keyword)
            
            db.session.commit()
    
    def test_matching_score_creation(self, app, sample_user, sample_resume, sample_jd):
        """Test matching score creation with valid data"""
        with app.app_context():
            matching_score = MatchingScore(
                user_id=sample_user.id,
                resume_id=sample_resume.id,
                job_description_id=sample_jd.id,
                overall_match_percentage=75.50,
                jaccard_similarity=0.6250,
                keyword_overlap_count=3,
                resume_keyword_count=4,
                jd_keyword_count=5
            )
            
            db.session.add(matching_score)
            db.session.commit()
            
            # Verify creation
            assert matching_score.id is not None
            assert float(matching_score.overall_match_percentage) == 75.50
            assert float(matching_score.jaccard_similarity) == 0.6250
            assert matching_score.keyword_overlap_count == 3
            assert matching_score.is_current == True
    
    def test_matching_score_to_dict(self, app, sample_user, sample_resume, sample_jd):
        """Test matching score to_dict method"""
        with app.app_context():
            matching_score = MatchingScore(
                user_id=sample_user.id,
                resume_id=sample_resume.id,
                job_description_id=sample_jd.id,
                overall_match_percentage=80.00,
                matched_keywords=['python', 'flask'],
                missing_keywords=['react', 'docker'],
                extra_keywords=['javascript']
            )
            
            db.session.add(matching_score)
            db.session.commit()
            
            score_dict = matching_score.to_dict()
            
            assert score_dict['id'] == matching_score.id
            assert score_dict['overall_match_percentage'] == 80.00
            assert score_dict['matched_keywords'] == ['python', 'flask']
            assert score_dict['missing_keywords'] == ['react', 'docker']
            assert score_dict['match_category'] == 'excellent'  # 80% is excellent
    
    def test_match_category_classification(self, app, sample_user, sample_resume, sample_jd):
        """Test match category classification"""
        with app.app_context():
            test_cases = [
                (85.0, 'excellent'),
                (70.0, 'good'),
                (50.0, 'fair'),
                (30.0, 'poor'),
                (10.0, 'very_poor')
            ]
            
            for percentage, expected_category in test_cases:
                matching_score = MatchingScore(
                    user_id=sample_user.id,
                    resume_id=sample_resume.id,
                    job_description_id=sample_jd.id,
                    overall_match_percentage=percentage
                )
                
                assert matching_score.get_match_category() == expected_category
    
    def test_create_matching_score_method(self, app, sample_user, sample_resume, sample_jd):
        """Test MatchingScore.create_matching_score class method"""
        with app.app_context():
            matching_score, error = MatchingScore.create_matching_score(
                user_id=sample_user.id,
                resume_id=sample_resume.id,
                job_description_id=sample_jd.id,
                overall_match_percentage=65.75,
                jaccard_similarity=0.5500,
                keyword_overlap_count=2
            )
            
            assert error is None
            assert matching_score is not None
            assert float(matching_score.overall_match_percentage) == 65.75
            assert matching_score.is_current == True
    
    def test_get_current_score(self, app, sample_user, sample_resume, sample_jd):
        """Test getting current matching score"""
        with app.app_context():
            # Create a matching score
            MatchingScore.create_matching_score(
                user_id=sample_user.id,
                resume_id=sample_resume.id,
                job_description_id=sample_jd.id,
                overall_match_percentage=70.00
            )
            
            # Retrieve current score
            current_score = MatchingScore.get_current_score(
                sample_user.id, sample_resume.id, sample_jd.id
            )
            
            assert current_score is not None
            assert float(current_score.overall_match_percentage) == 70.00
            assert current_score.is_current == True

class TestMatchingCalculator:
    """Test cases for MatchingCalculator functionality"""
    
    def test_calculator_initialization(self):
        """Test matching calculator initialization"""
        calculator = MatchingCalculator(algorithm_version='1.0')
        assert calculator.algorithm_version == '1.0'
        assert len(calculator.keyword_weights) > 0
    
    def test_jaccard_similarity_calculation(self):
        """Test Jaccard similarity calculation"""
        calculator = MatchingCalculator()
        
        # Test identical sets
        set1 = {'python', 'flask', 'postgresql'}
        set2 = {'python', 'flask', 'postgresql'}
        similarity = calculator.calculate_jaccard_similarity(set1, set2)
        assert similarity == 1.0
        
        # Test no overlap
        set1 = {'python', 'flask'}
        set2 = {'java', 'spring'}
        similarity = calculator.calculate_jaccard_similarity(set1, set2)
        assert similarity == 0.0
        
        # Test partial overlap
        set1 = {'python', 'flask', 'postgresql'}
        set2 = {'python', 'django', 'mysql'}
        similarity = calculator.calculate_jaccard_similarity(set1, set2)
        expected = 1 / 5  # 1 common, 5 total unique
        assert abs(similarity - expected) < 0.001
    
    def test_keyword_overlap_analysis(self):
        """Test keyword overlap analysis"""
        calculator = MatchingCalculator()
        
        resume_keywords = [
            {'keyword': 'python', 'keyword_type': 'technology'},
            {'keyword': 'flask', 'keyword_type': 'framework'},
            {'keyword': 'postgresql', 'keyword_type': 'technology'}
        ]
        
        jd_keywords = [
            {'keyword': 'python', 'keyword_type': 'technology'},
            {'keyword': 'django', 'keyword_type': 'framework'},
            {'keyword': 'mysql', 'keyword_type': 'technology'}
        ]
        
        analysis = calculator.analyze_keyword_overlap(resume_keywords, jd_keywords)
        
        assert analysis['overlap_count'] == 1  # Only 'python' matches
        assert 'python' in analysis['matched_keywords']
        assert 'django' in analysis['missing_keywords']
        assert 'flask' in analysis['extra_keywords']
        assert analysis['resume_keyword_count'] == 3
        assert analysis['jd_keyword_count'] == 3
    
    def test_category_matches_calculation(self):
        """Test category-specific match calculation"""
        calculator = MatchingCalculator()
        
        resume_keywords = [
            {'keyword': 'python', 'keyword_type': 'technology'},
            {'keyword': 'flask', 'keyword_type': 'framework'},
            {'keyword': 'senior', 'keyword_type': 'experience'}
        ]
        
        jd_keywords = [
            {'keyword': 'python', 'keyword_type': 'technology'},
            {'keyword': 'java', 'keyword_type': 'technology'},
            {'keyword': 'flask', 'keyword_type': 'framework'},
            {'keyword': 'senior', 'keyword_type': 'experience'}
        ]
        
        category_matches = calculator.calculate_category_matches(resume_keywords, jd_keywords)
        
        # Technology: 1 match out of 2 required = 50%
        assert category_matches['technology'] == 50.0
        # Framework: 1 match out of 1 required = 100%
        assert category_matches['framework'] == 100.0
        # Experience: 1 match out of 1 required = 100%
        assert category_matches['experience'] == 100.0
    
    def test_get_matching_calculator_singleton(self):
        """Test singleton pattern for matching calculator"""
        calculator1 = get_matching_calculator()
        calculator2 = get_matching_calculator()
        
        assert calculator1 is calculator2  # Should be the same instance

class TestMatchingAPI:
    """Test cases for Matching API endpoints"""
    
    @pytest.fixture
    def app(self):
        """Create test Flask application"""
        app = create_app()
        app.config['TESTING'] = True
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        app.config['JWT_SECRET_KEY'] = 'test-secret-key'
        
        with app.app_context():
            db.create_all()
            yield app
            db.drop_all()
    
    @pytest.fixture
    def client(self, app):
        """Create test client"""
        return app.test_client()
    
    @pytest.fixture
    def auth_user(self, app, client):
        """Create and authenticate a user"""
        with app.app_context():
            # Register user
            user_data = {
                'email': '<EMAIL>',
                'password': 'password123',
                'first_name': 'Test',
                'last_name': 'User'
            }
            
            response = client.post('/api/register', 
                                 data=json.dumps(user_data),
                                 content_type='application/json')
            
            # Login user
            login_data = {
                'email': '<EMAIL>',
                'password': 'password123'
            }
            
            response = client.post('/api/login',
                                 data=json.dumps(login_data),
                                 content_type='application/json')
            
            data = json.loads(response.data)
            return data['tokens']['access_token']
    
    @pytest.fixture
    def sample_documents_with_keywords(self, app, auth_user):
        """Create sample documents with keywords"""
        with app.app_context():
            # Get user
            user = User.query.filter_by(email='<EMAIL>').first()
            
            # Create resume
            resume = Resume(
                user_id=user.id,
                original_filename='test_resume.pdf',
                extracted_text='Python developer with Flask experience',
                keywords_extracted=True
            )
            db.session.add(resume)
            
            # Create job description
            jd = JobDescription(
                user_id=user.id,
                title='Python Developer',
                job_description_text='Looking for Python developer with Flask experience',
                keywords_extracted=True
            )
            db.session.add(jd)
            db.session.commit()
            
            # Create keywords
            resume_keywords = [
                {'keyword': 'python', 'keyword_type': 'technology'},
                {'keyword': 'flask', 'keyword_type': 'framework'}
            ]
            
            for kw_data in resume_keywords:
                keyword = Keyword(
                    user_id=user.id,
                    resume_id=resume.id,
                    **kw_data
                )
                db.session.add(keyword)
            
            jd_keywords = [
                {'keyword': 'python', 'keyword_type': 'technology'},
                {'keyword': 'flask', 'keyword_type': 'framework'},
                {'keyword': 'react', 'keyword_type': 'framework'}
            ]
            
            for kw_data in jd_keywords:
                keyword = Keyword(
                    user_id=user.id,
                    job_description_id=jd.id,
                    **kw_data
                )
                db.session.add(keyword)
            
            db.session.commit()
            
            return {'resume_id': resume.id, 'jd_id': jd.id}
    
    def test_calculate_match_success(self, client, auth_user, sample_documents_with_keywords):
        """Test successful matching score calculation"""
        match_data = {
            'resume_id': sample_documents_with_keywords['resume_id'],
            'job_description_id': sample_documents_with_keywords['jd_id'],
            'calculation_method': 'jaccard'
        }
        
        response = client.post('/api/calculate_match',
                             data=json.dumps(match_data),
                             content_type='application/json',
                             headers={'Authorization': f'Bearer {auth_user}'})
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] == True
        assert 'matching_score' in data
        assert data['matching_score']['overall_match_percentage'] > 0
    
    def test_calculate_match_validation_errors(self, client, auth_user):
        """Test matching calculation with validation errors"""
        # Test missing resume_id
        match_data = {
            'job_description_id': 'test-jd-id',
            'calculation_method': 'jaccard'
        }
        
        response = client.post('/api/calculate_match',
                             data=json.dumps(match_data),
                             content_type='application/json',
                             headers={'Authorization': f'Bearer {auth_user}'})
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['success'] == False
        assert 'resume id is required' in data['message'].lower()
    
    def test_get_matching_scores(self, client, auth_user, sample_documents_with_keywords):
        """Test getting matching scores list"""
        # First calculate a matching score
        match_data = {
            'resume_id': sample_documents_with_keywords['resume_id'],
            'job_description_id': sample_documents_with_keywords['jd_id'],
            'calculation_method': 'jaccard'
        }
        
        client.post('/api/calculate_match',
                   data=json.dumps(match_data),
                   content_type='application/json',
                   headers={'Authorization': f'Bearer {auth_user}'})
        
        # Get matching scores
        response = client.get('/api/matching_scores',
                            headers={'Authorization': f'Bearer {auth_user}'})
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] == True
        assert 'matching_scores' in data
        assert len(data['matching_scores']) > 0
    
    def test_get_best_matches(self, client, auth_user, sample_documents_with_keywords):
        """Test getting best matches"""
        # First calculate a matching score
        match_data = {
            'resume_id': sample_documents_with_keywords['resume_id'],
            'job_description_id': sample_documents_with_keywords['jd_id'],
            'calculation_method': 'jaccard'
        }
        
        client.post('/api/calculate_match',
                   data=json.dumps(match_data),
                   content_type='application/json',
                   headers={'Authorization': f'Bearer {auth_user}'})
        
        # Get best matches
        response = client.get('/api/best_matches?min_score=0',
                            headers={'Authorization': f'Bearer {auth_user}'})
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] == True
        assert 'best_matches' in data
    
    def test_matching_health_check(self, client):
        """Test matching health check endpoint"""
        response = client.get('/api/matching_health')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] == True
        assert 'US-06' in data['service']

if __name__ == '__main__':
    pytest.main([__file__])
